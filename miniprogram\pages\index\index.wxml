<!--pages/index/index.wxml-->
<swiper
  class="banner-swiper"
  indicator-dots="true"
  autoplay="true"
  interval="4000"
  duration="500"
  circular="true"
  current="{{currentBannerIndex}}"
  bindchange="onBannerChange"
>
  <swiper-item
    wx:for="{{banners}}"
    wx:key="id"
    wx:for-index="index"
    data-index="{{index}}"
    bindtap="onBannerTap"
  >
    <image
      src="{{item.image}}"
      mode="aspectFill"
      style="width: 100%; height: 100%; position: relative; left: -37rpx; top: 688rpx"
    />

    <!-- 轮播图文字遮罩 - 后续可能会用到
    <view class="banner-overlay">
      <view class="banner-content">
        <view class="banner-title">{{item.title}}</view>
        <view class="banner-subtitle">{{item.subtitle}}</view>
      </view>
    </view>
    -->
  </swiper-item>
</swiper>