<!--pages/index/index.wxml-->
<view class="container">
  <!-- 轮播图区域 -->
  <view class="banner-section">
    <swiper
      class="banner-swiper"
      indicator-dots="true"
      autoplay="false"
      interval="4000"
      duration="500"
      circular="true"
      current="{{currentBannerIndex}}"
      bindchange="onBannerChange"
    >
      <swiper-item
        wx:for="{{banners}}"
        wx:key="id"
        wx:for-index="index"
        class="banner-item"
        data-index="{{index}}"
        bindtap="onBannerTap"
      >
        <!-- 轮播图图片 -->
        <image
          class="banner-image"
          src="{{item.image}}"
          mode="aspectFill"
          data-index="{{index}}"
          bindload="onBannerImageLoad"
          binderror="onBannerImageError"
          lazy-load="true"
        />

        <!-- 轮播图遮罩和内容 -->
        <view class="banner-overlay">
          <view class="banner-content">
            <view class="banner-title">{{item.title}}</view>
            <view class="banner-subtitle">{{item.subtitle}}</view>
          </view>
        </view>

        <!-- 图片加载状态 -->
        <view class="image-loading" wx:if="{{item.loading}}">
          <view class="loading-spinner"></view>
          <view class="loading-text">加载中...</view>
        </view>
      </swiper-item>
    </swiper>

    <!-- 滑动提示 -->
    <view class="scroll-hint" wx:if="{{currentBannerIndex === 0}}">
      <view class="scroll-arrow">↓</view>
    </view>
  </view>

  <!-- 内容区域 -->
  <view class="content-section">
    <!-- 品牌介绍图片区域 -->
    <view class="brand-intro-section">
      <image
        class="brand-intro-image"
        src="{{brandIntroImage}}"
        mode="aspectFill"
        bindload="onBrandIntroImageLoad"
        binderror="onBrandIntroImageError"
        lazy-load="true"
      />

      <!-- 品牌介绍图片加载状态 -->
      <view class="image-loading" wx:if="{{brandIntroLoading}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 品牌介绍占位符 -->
      <view class="image-placeholder" wx:if="{{!brandIntroImage}}">
        <view class="placeholder-text">品牌介绍</view>
        <view class="placeholder-desc">管理员上传后显示</view>
      </view>
    </view>

    <!-- 品牌历程图片区域 -->
    <view class="brand-history-section">
      <image
        class="brand-history-image"
        src="{{brandHistoryImage}}"
        mode="aspectFill"
        bindload="onBrandHistoryImageLoad"
        binderror="onBrandHistoryImageError"
        lazy-load="true"
      />

      <!-- 品牌历程图片加载状态 -->
      <view class="image-loading" wx:if="{{brandHistoryLoading}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">加载中...</view>
      </view>

      <!-- 品牌历程占位符 -->
      <view class="image-placeholder" wx:if="{{!brandHistoryImage}}">
        <view class="placeholder-text">品牌历程</view>
        <view class="placeholder-desc">管理员上传后显示</view>
      </view>
    </view>
  </view>
</view>