/**
 * 用户状态管理器
 * 统一管理用户登录状态、用户信息、权限管理等功能
 */

import { USER_STATUS, EVENT_NAMES, STORAGE_KEYS } from '../constants/config.js';

class UserManager {
  constructor() {
    this.userInfo = null;
    this.loginStatus = USER_STATUS.NOT_LOGIN;
    this.permissions = [];
    this.token = '';
    this.isInitialized = false;
    this.eventListeners = new Map();
    this.loginPromise = null;
  }

  /**
   * 初始化用户管理器
   */
  init() {
    try {
      // 从本地存储恢复用户信息
      this.loadUserFromStorage();
      
      // 检查登录状态
      this.checkLoginStatus();
      
      this.isInitialized = true;
      console.log('[UserManager] 用户管理器初始化成功', { 
        status: this.loginStatus,
        hasUserInfo: !!this.userInfo 
      });
      
      return true;
    } catch (error) {
      console.error('[UserManager] 用户管理器初始化失败:', error);
      return false;
    }
  }

  /**
   * 用户登录
   * @param {Object} options - 登录选项
   */
  async login(options = {}) {
    // 如果正在登录中，返回当前的登录Promise
    if (this.loginPromise) {
      return this.loginPromise;
    }

    this.loginPromise = this._performLogin(options);
    
    try {
      const result = await this.loginPromise;
      return result;
    } finally {
      this.loginPromise = null;
    }
  }

  /**
   * 执行登录逻辑
   * @param {Object} options - 登录选项
   */
  async _performLogin(options = {}) {
    try {
      this.setLoginStatus(USER_STATUS.LOGGING);
      
      const { 
        withCredentials = true,
        timeout = 10000,
        autoGetUserInfo = true 
      } = options;

      // 1. 微信登录获取code
      const loginResult = await this.wxLogin(timeout);
      if (!loginResult.success) {
        throw new Error(loginResult.error);
      }

      // 2. 获取用户信息（如果需要）
      let userProfile = null;
      if (autoGetUserInfo) {
        const profileResult = await this.getUserProfile();
        if (profileResult.success) {
          userProfile = profileResult.userInfo;
        }
      }

      // 3. 调用后端登录接口（如果配置了）
      let serverResult = null;
      if (options.serverLogin && typeof options.serverLogin === 'function') {
        serverResult = await options.serverLogin({
          code: loginResult.code,
          userInfo: userProfile
        });
      }

      // 4. 设置用户信息
      const userData = {
        openid: serverResult?.openid || '',
        unionid: serverResult?.unionid || '',
        sessionKey: serverResult?.sessionKey || '',
        userInfo: userProfile,
        loginTime: Date.now(),
        ...serverResult?.userData
      };

      this.setUserInfo(userData);
      this.setToken(serverResult?.token || '');
      this.setPermissions(serverResult?.permissions || []);
      this.setLoginStatus(USER_STATUS.LOGGED);

      // 保存到本地存储
      this.saveUserToStorage();

      // 触发登录成功事件
      this.emit(EVENT_NAMES.USER_LOGIN, {
        userInfo: this.userInfo,
        token: this.token
      });

      console.log('[UserManager] 用户登录成功');
      return {
        success: true,
        userInfo: this.userInfo,
        token: this.token
      };

    } catch (error) {
      this.setLoginStatus(USER_STATUS.LOGIN_FAILED);
      console.error('[UserManager] 用户登录失败:', error);
      
      return {
        success: false,
        error: error.message || '登录失败'
      };
    }
  }

  /**
   * 微信登录
   * @param {number} timeout - 超时时间
   */
  async wxLogin(timeout = 10000) {
    return new Promise((resolve) => {
      const timer = setTimeout(() => {
        resolve({
          success: false,
          error: '登录超时'
        });
      }, timeout);

      wx.login({
        success: (res) => {
          clearTimeout(timer);
          if (res.code) {
            resolve({
              success: true,
              code: res.code
            });
          } else {
            resolve({
              success: false,
              error: res.errMsg || '获取登录凭证失败'
            });
          }
        },
        fail: (err) => {
          clearTimeout(timer);
          resolve({
            success: false,
            error: err.errMsg || '微信登录失败'
          });
        }
      });
    });
  }

  /**
   * 获取用户信息
   */
  async getUserProfile() {
    return new Promise((resolve) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (res) => {
          resolve({
            success: true,
            userInfo: res.userInfo
          });
        },
        fail: (err) => {
          resolve({
            success: false,
            error: err.errMsg || '获取用户信息失败'
          });
        }
      });
    });
  }

  /**
   * 用户登出
   */
  async logout() {
    try {
      // 清除用户信息
      this.userInfo = null;
      this.token = '';
      this.permissions = [];
      this.setLoginStatus(USER_STATUS.NOT_LOGIN);

      // 清除本地存储
      this.clearUserFromStorage();

      // 触发登出事件
      this.emit(EVENT_NAMES.USER_LOGOUT, {});

      console.log('[UserManager] 用户登出成功');
      return {
        success: true
      };
    } catch (error) {
      console.error('[UserManager] 用户登出失败:', error);
      return {
        success: false,
        error: error.message || '登出失败'
      };
    }
  }

  /**
   * 检查登录状态
   */
  async checkLoginStatus() {
    try {
      // 检查本地是否有用户信息和token
      if (!this.userInfo || !this.token) {
        this.setLoginStatus(USER_STATUS.NOT_LOGIN);
        return false;
      }

      // 检查登录时间是否过期（可选）
      const loginTime = this.userInfo.loginTime;
      if (loginTime) {
        const expireTime = 7 * 24 * 60 * 60 * 1000; // 7天
        if (Date.now() - loginTime > expireTime) {
          this.logout();
          return false;
        }
      }

      // 可以在这里添加服务器验证token的逻辑
      
      this.setLoginStatus(USER_STATUS.LOGGED);
      return true;
    } catch (error) {
      console.error('[UserManager] 检查登录状态失败:', error);
      this.setLoginStatus(USER_STATUS.NOT_LOGIN);
      return false;
    }
  }

  /**
   * 刷新用户信息
   */
  async refreshUserInfo() {
    if (!this.isLoggedIn()) {
      return {
        success: false,
        error: '用户未登录'
      };
    }

    try {
      // 重新获取用户信息
      const profileResult = await this.getUserProfile();
      if (profileResult.success) {
        this.userInfo.userInfo = profileResult.userInfo;
        this.userInfo.updateTime = Date.now();
        this.saveUserToStorage();
        
        return {
          success: true,
          userInfo: this.userInfo
        };
      } else {
        return {
          success: false,
          error: profileResult.error
        };
      }
    } catch (error) {
      console.error('[UserManager] 刷新用户信息失败:', error);
      return {
        success: false,
        error: error.message || '刷新用户信息失败'
      };
    }
  }

  /**
   * 设置用户信息
   * @param {Object} userInfo - 用户信息
   */
  setUserInfo(userInfo) {
    this.userInfo = userInfo;
  }

  /**
   * 获取用户信息
   */
  getUserInfo() {
    return this.userInfo;
  }

  /**
   * 设置登录状态
   * @param {string} status - 登录状态
   */
  setLoginStatus(status) {
    const oldStatus = this.loginStatus;
    this.loginStatus = status;
    
    if (oldStatus !== status) {
      console.log('[UserManager] 登录状态变更:', { from: oldStatus, to: status });
    }
  }

  /**
   * 获取登录状态
   */
  getLoginStatus() {
    return this.loginStatus;
  }

  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    return this.loginStatus === USER_STATUS.LOGGED;
  }

  /**
   * 检查是否正在登录
   */
  isLogging() {
    return this.loginStatus === USER_STATUS.LOGGING;
  }

  /**
   * 设置token
   * @param {string} token - 用户token
   */
  setToken(token) {
    this.token = token;
  }

  /**
   * 获取token
   */
  getToken() {
    return this.token;
  }

  /**
   * 设置权限
   * @param {Array} permissions - 权限列表
   */
  setPermissions(permissions) {
    this.permissions = Array.isArray(permissions) ? permissions : [];
  }

  /**
   * 获取权限
   */
  getPermissions() {
    return this.permissions;
  }

  /**
   * 检查是否有指定权限
   * @param {string} permission - 权限名称
   */
  hasPermission(permission) {
    return this.permissions.includes(permission);
  }

  /**
   * 检查是否有任一权限
   * @param {Array} permissions - 权限列表
   */
  hasAnyPermission(permissions) {
    return permissions.some(permission => this.hasPermission(permission));
  }

  /**
   * 检查是否有所有权限
   * @param {Array} permissions - 权限列表
   */
  hasAllPermissions(permissions) {
    return permissions.every(permission => this.hasPermission(permission));
  }

  /**
   * 保存用户信息到本地存储
   */
  saveUserToStorage() {
    try {
      if (this.userInfo) {
        wx.setStorageSync(STORAGE_KEYS.USER_INFO, this.userInfo);
      }
      if (this.token) {
        wx.setStorageSync(STORAGE_KEYS.USER_TOKEN, this.token);
      }
      wx.setStorageSync(STORAGE_KEYS.LAST_LOGIN_TIME, Date.now());
    } catch (error) {
      console.error('[UserManager] 保存用户信息到本地存储失败:', error);
    }
  }

  /**
   * 从本地存储加载用户信息
   */
  loadUserFromStorage() {
    try {
      const userInfo = wx.getStorageSync(STORAGE_KEYS.USER_INFO);
      const token = wx.getStorageSync(STORAGE_KEYS.USER_TOKEN);
      
      if (userInfo) {
        this.userInfo = userInfo;
      }
      if (token) {
        this.token = token;
      }
    } catch (error) {
      console.error('[UserManager] 从本地存储加载用户信息失败:', error);
    }
  }

  /**
   * 清除本地存储的用户信息
   */
  clearUserFromStorage() {
    try {
      wx.removeStorageSync(STORAGE_KEYS.USER_INFO);
      wx.removeStorageSync(STORAGE_KEYS.USER_TOKEN);
      wx.removeStorageSync(STORAGE_KEYS.LAST_LOGIN_TIME);
    } catch (error) {
      console.error('[UserManager] 清除本地存储的用户信息失败:', error);
    }
  }

  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * 移除事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('[UserManager] 事件回调执行失败:', error);
        }
      });
    }
  }
}

// 创建单例
const userManager = new UserManager();

export default userManager;
