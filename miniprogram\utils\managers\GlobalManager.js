/**
 * 全局管理器
 * 统一管理所有全局服务的初始化和生命周期
 */

import CloudManager from './CloudManager.js';
import CacheManager from './CacheManager.js';
import LogManager from './LogManager.js';
import UserManager from './UserManager.js';
import NetworkManager from './NetworkManager.js';
import { LOG_LEVELS, EVENT_NAMES } from '../constants/config.js';

class GlobalManager {
  constructor() {
    this.isInitialized = false;
    this.initStartTime = 0;
    this.initEndTime = 0;
    this.managers = {
      log: LogManager,
      cache: CacheManager,
      network: NetworkManager,
      user: UserManager,
      cloud: CloudManager
    };
    this.initResults = {};
    this.eventListeners = new Map();
  }

  /**
   * 初始化所有管理器
   * @param {Object} config - 配置选项
   */
  async init(config = {}) {
    if (this.isInitialized) {
      console.warn('[GlobalManager] 全局管理器已经初始化');
      return this.initResults;
    }

    this.initStartTime = Date.now();
    console.log('[GlobalManager] 开始初始化全局管理器...');

    try {
      const {
        log: logConfig = {},
        cache: cacheConfig = {},
        network: networkConfig = {},
        user: userConfig = {},
        cloud: cloudConfig = {}
      } = config;

      // 按顺序初始化管理器
      this.initResults = await this.initializeManagers({
        logConfig,
        cacheConfig,
        networkConfig,
        userConfig,
        cloudConfig
      });

      // 设置管理器之间的关联
      this.setupManagerRelations();

      // 设置全局错误处理
      this.setupGlobalErrorHandling();

      // 设置应用生命周期监听
      this.setupAppLifecycleListeners();

      this.isInitialized = true;
      this.initEndTime = Date.now();

      const initTime = this.initEndTime - this.initStartTime;
      console.log(`[GlobalManager] 全局管理器初始化完成，耗时: ${initTime}ms`);

      // 记录初始化日志
      LogManager.info('全局管理器初始化完成', {
        initTime,
        results: this.initResults
      });

      return this.initResults;
    } catch (error) {
      console.error('[GlobalManager] 全局管理器初始化失败:', error);
      LogManager.error('全局管理器初始化失败', error);
      throw error;
    }
  }

  /**
   * 按顺序初始化各个管理器
   * @param {Object} configs - 各管理器配置
   */
  async initializeManagers(configs) {
    const results = {};

    // 1. 首先初始化日志管理器
    console.log('[GlobalManager] 初始化日志管理器...');
    results.log = LogManager.init({
      level: LOG_LEVELS.INFO,
      maxLogCount: 1000,
      enableUpload: false,
      ...configs.logConfig
    });

    // 2. 初始化缓存管理器
    console.log('[GlobalManager] 初始化缓存管理器...');
    results.cache = CacheManager.init();

    // 3. 初始化网络管理器
    console.log('[GlobalManager] 初始化网络管理器...');
    results.network = NetworkManager.init();

    // 4. 初始化用户管理器
    console.log('[GlobalManager] 初始化用户管理器...');
    results.user = UserManager.init();

    // 5. 最后初始化云服务管理器
    console.log('[GlobalManager] 初始化云服务管理器...');
    const cloudEnv = configs.cloudConfig.env || '';
    results.cloud = CloudManager.init(cloudEnv);

    return results;
  }

  /**
   * 设置管理器之间的关联
   */
  setupManagerRelations() {
    // 网络状态变化时的处理
    NetworkManager.on(EVENT_NAMES.NETWORK_CHANGE, (data) => {
      LogManager.info('网络状态变化', data);
      
      // 可以在这里添加网络状态变化时的其他处理逻辑
      if (data.isOnline) {
        // 网络恢复时，可以触发一些同步操作
        this.emit('network_restored', data);
      } else {
        // 网络断开时的处理
        this.emit('network_lost', data);
      }
    });

    // 用户登录状态变化时的处理
    UserManager.on(EVENT_NAMES.USER_LOGIN, (data) => {
      LogManager.info('用户登录成功', { userId: data.userInfo?.openid });
      CacheManager.set('last_login_user', data.userInfo, 0, true); // 永久缓存
      this.emit('user_logged_in', data);
    });

    UserManager.on(EVENT_NAMES.USER_LOGOUT, (data) => {
      LogManager.info('用户登出');
      CacheManager.delete('last_login_user');
      this.emit('user_logged_out', data);
    });

    // 缓存清理时的处理
    CacheManager.on(EVENT_NAMES.CACHE_CLEAR, (data) => {
      LogManager.info('缓存已清理', data);
    });

    // 错误发生时的处理
    LogManager.on(EVENT_NAMES.ERROR_OCCURRED, (data) => {
      // 可以在这里添加错误上报逻辑
      this.emit('error_occurred', data);
    });
  }

  /**
   * 设置全局错误处理
   */
  setupGlobalErrorHandling() {
    // 监听小程序错误
    wx.onError((error) => {
      LogManager.error('小程序全局错误', {
        message: error,
        stack: error.stack || '',
        timestamp: Date.now()
      });
    });

    // 监听未处理的Promise拒绝
    wx.onUnhandledRejection((res) => {
      LogManager.error('未处理的Promise拒绝', {
        reason: res.reason,
        promise: res.promise,
        timestamp: Date.now()
      });
    });
  }

  /**
   * 设置应用生命周期监听
   */
  setupAppLifecycleListeners() {
    // 应用进入前台
    wx.onAppShow(() => {
      LogManager.info('应用进入前台');
      
      // 检查网络状态
      NetworkManager.getNetworkStatus();
      
      // 检查用户登录状态
      UserManager.checkLoginStatus();
      
      // 清理过期缓存
      CacheManager.clearExpired();
      
      this.emit('app_show');
    });

    // 应用进入后台
    wx.onAppHide(() => {
      LogManager.info('应用进入后台');
      
      // 可以在这里执行一些清理工作
      this.emit('app_hide');
    });
  }

  /**
   * 获取指定管理器
   * @param {string} name - 管理器名称
   */
  getManager(name) {
    const manager = this.managers[name];
    if (!manager) {
      throw new Error(`未知的管理器: ${name}`);
    }
    return manager;
  }

  /**
   * 获取所有管理器
   */
  getAllManagers() {
    return this.managers;
  }

  /**
   * 获取初始化结果
   */
  getInitResults() {
    return this.initResults;
  }

  /**
   * 检查是否已初始化
   */
  isReady() {
    return this.isInitialized;
  }

  /**
   * 获取初始化耗时
   */
  getInitTime() {
    return this.initEndTime - this.initStartTime;
  }

  /**
   * 获取系统状态
   */
  getSystemStatus() {
    if (!this.isInitialized) {
      return {
        initialized: false,
        message: '系统未初始化'
      };
    }

    return {
      initialized: true,
      initTime: this.getInitTime(),
      managers: {
        log: LogManager.isInitialized,
        cache: CacheManager.isInitialized,
        network: NetworkManager.isInitialized,
        user: UserManager.isInitialized,
        cloud: CloudManager.isInitialized
      },
      network: NetworkManager.getStats(),
      cache: CacheManager.getStats(),
      user: {
        isLoggedIn: UserManager.isLoggedIn(),
        loginStatus: UserManager.getLoginStatus()
      }
    };
  }

  /**
   * 重新初始化指定管理器
   * @param {string} managerName - 管理器名称
   * @param {Object} config - 配置选项
   */
  async reinitializeManager(managerName, config = {}) {
    const manager = this.getManager(managerName);
    
    try {
      LogManager.info(`重新初始化管理器: ${managerName}`);
      
      let result = false;
      switch (managerName) {
        case 'log':
          result = manager.init(config);
          break;
        case 'cache':
          result = manager.init();
          break;
        case 'network':
          result = manager.init();
          break;
        case 'user':
          result = manager.init();
          break;
        case 'cloud':
          result = manager.init(config.env || '');
          break;
        default:
          throw new Error(`不支持重新初始化管理器: ${managerName}`);
      }
      
      this.initResults[managerName] = result;
      LogManager.info(`管理器重新初始化完成: ${managerName}`, { result });
      
      return result;
    } catch (error) {
      LogManager.error(`管理器重新初始化失败: ${managerName}`, error);
      throw error;
    }
  }

  /**
   * 销毁所有管理器
   */
  destroy() {
    try {
      LogManager.info('开始销毁全局管理器');
      
      // 清理事件监听器
      this.eventListeners.clear();
      
      // 清理缓存
      CacheManager.clear(false); // 不清理持久化缓存
      
      // 清空网络请求队列
      NetworkManager.clearOfflineQueue();
      
      this.isInitialized = false;
      LogManager.info('全局管理器销毁完成');
      
    } catch (error) {
      console.error('[GlobalManager] 销毁全局管理器失败:', error);
    }
  }

  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * 移除事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('[GlobalManager] 事件回调执行失败:', error);
        }
      });
    }
  }
}

// 创建单例
const globalManager = new GlobalManager();

// 导出单例和各个管理器
export default globalManager;

export {
  CloudManager,
  CacheManager,
  LogManager,
  UserManager,
  NetworkManager
};
