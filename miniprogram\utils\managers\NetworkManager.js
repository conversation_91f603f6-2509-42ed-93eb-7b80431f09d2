/**
 * 网络状态管理器
 * 统一管理网络状态监听、网络请求管理、离线处理等功能
 */

import { NETWORK_STATUS, NETWORK_CONFIG, EVENT_NAMES } from '../constants/config.js';

class NetworkManager {
  constructor() {
    this.networkStatus = NETWORK_STATUS.UNKNOWN;
    this.networkType = 'unknown';
    this.isInitialized = false;
    this.eventListeners = new Map();
    this.requestQueue = [];
    this.isProcessingQueue = false;
    this.requestInterceptors = [];
    this.responseInterceptors = [];
  }

  /**
   * 初始化网络管理器
   */
  init() {
    try {
      // 获取当前网络状态
      this.getNetworkStatus();
      
      // 监听网络状态变化
      this.startNetworkMonitoring();
      
      this.isInitialized = true;
      console.log('[NetworkManager] 网络管理器初始化成功', {
        status: this.networkStatus,
        type: this.networkType
      });
      
      return true;
    } catch (error) {
      console.error('[NetworkManager] 网络管理器初始化失败:', error);
      return false;
    }
  }

  /**
   * 获取网络状态
   */
  getNetworkStatus() {
    try {
      const networkInfo = wx.getNetworkTypeSync();
      this.updateNetworkStatus(networkInfo.networkType);
    } catch (error) {
      console.error('[NetworkManager] 获取网络状态失败:', error);
      this.updateNetworkStatus('unknown');
    }
  }

  /**
   * 开始网络状态监听
   */
  startNetworkMonitoring() {
    wx.onNetworkStatusChange((res) => {
      this.updateNetworkStatus(res.networkType, res.isConnected);
    });
  }

  /**
   * 更新网络状态
   * @param {string} networkType - 网络类型
   * @param {boolean} isConnected - 是否连接
   */
  updateNetworkStatus(networkType, isConnected = null) {
    const oldStatus = this.networkStatus;
    const oldType = this.networkType;
    
    this.networkType = networkType;
    
    // 判断网络状态
    if (isConnected === false || networkType === 'none') {
      this.networkStatus = NETWORK_STATUS.OFFLINE;
    } else if (networkType && networkType !== 'none' && networkType !== 'unknown') {
      this.networkStatus = NETWORK_STATUS.ONLINE;
    } else {
      this.networkStatus = NETWORK_STATUS.UNKNOWN;
    }

    // 如果状态发生变化，触发事件
    if (oldStatus !== this.networkStatus || oldType !== this.networkType) {
      console.log('[NetworkManager] 网络状态变化:', {
        from: { status: oldStatus, type: oldType },
        to: { status: this.networkStatus, type: this.networkType }
      });

      this.emit(EVENT_NAMES.NETWORK_CHANGE, {
        status: this.networkStatus,
        type: this.networkType,
        isOnline: this.isOnline(),
        isOffline: this.isOffline()
      });

      // 如果从离线变为在线，处理离线队列
      if (oldStatus === NETWORK_STATUS.OFFLINE && this.networkStatus === NETWORK_STATUS.ONLINE) {
        this.processOfflineQueue();
      }
    }
  }

  /**
   * 检查是否在线
   */
  isOnline() {
    return this.networkStatus === NETWORK_STATUS.ONLINE;
  }

  /**
   * 检查是否离线
   */
  isOffline() {
    return this.networkStatus === NETWORK_STATUS.OFFLINE;
  }

  /**
   * 获取网络类型
   */
  getNetworkType() {
    return this.networkType;
  }

  /**
   * 统一的网络请求方法
   * @param {Object} options - 请求选项
   */
  async request(options) {
    const {
      url,
      method = 'GET',
      data = {},
      header = {},
      timeout = NETWORK_CONFIG.REQUEST_TIMEOUT,
      enableQueue = true,
      retryCount = NETWORK_CONFIG.MAX_RETRY_COUNT,
      retryInterval = NETWORK_CONFIG.RETRY_INTERVAL
    } = options;

    // 如果离线且启用队列，添加到离线队列
    if (this.isOffline() && enableQueue) {
      return this.addToOfflineQueue(options);
    }

    // 应用请求拦截器
    const processedOptions = await this.applyRequestInterceptors({
      url,
      method,
      data,
      header,
      timeout
    });

    // 执行请求（带重试机制）
    return this.executeRequestWithRetry(processedOptions, retryCount, retryInterval);
  }

  /**
   * 执行带重试机制的请求
   * @param {Object} options - 请求选项
   * @param {number} retryCount - 重试次数
   * @param {number} retryInterval - 重试间隔
   */
  async executeRequestWithRetry(options, retryCount, retryInterval) {
    let lastError = null;
    
    for (let i = 0; i <= retryCount; i++) {
      try {
        const result = await this.executeRequest(options);
        
        // 应用响应拦截器
        return await this.applyResponseInterceptors(result, options);
      } catch (error) {
        lastError = error;
        
        // 如果不是最后一次尝试，等待后重试
        if (i < retryCount) {
          console.warn(`[NetworkManager] 请求失败，${retryInterval}ms后进行第${i + 1}次重试:`, error);
          await this.delay(retryInterval);
        }
      }
    }
    
    // 所有重试都失败了
    throw lastError;
  }

  /**
   * 执行单次请求
   * @param {Object} options - 请求选项
   */
  async executeRequest(options) {
    return new Promise((resolve, reject) => {
      const requestTask = wx.request({
        ...options,
        success: (res) => {
          console.log('[NetworkManager] 请求成功:', { url: options.url, status: res.statusCode });
          resolve(res);
        },
        fail: (err) => {
          console.error('[NetworkManager] 请求失败:', { url: options.url, error: err });
          reject(new Error(err.errMsg || '网络请求失败'));
        }
      });

      // 设置超时
      if (options.timeout) {
        setTimeout(() => {
          requestTask.abort();
          reject(new Error('请求超时'));
        }, options.timeout);
      }
    });
  }

  /**
   * 添加到离线队列
   * @param {Object} options - 请求选项
   */
  addToOfflineQueue(options) {
    return new Promise((resolve, reject) => {
      const queueItem = {
        options,
        resolve,
        reject,
        timestamp: Date.now()
      };
      
      this.requestQueue.push(queueItem);
      console.log('[NetworkManager] 请求已添加到离线队列:', options.url);
    });
  }

  /**
   * 处理离线队列
   */
  async processOfflineQueue() {
    if (this.isProcessingQueue || this.requestQueue.length === 0) {
      return;
    }

    this.isProcessingQueue = true;
    console.log('[NetworkManager] 开始处理离线队列:', this.requestQueue.length);

    const queue = [...this.requestQueue];
    this.requestQueue = [];

    for (const queueItem of queue) {
      try {
        const result = await this.executeRequestWithRetry(
          queueItem.options,
          NETWORK_CONFIG.MAX_RETRY_COUNT,
          NETWORK_CONFIG.RETRY_INTERVAL
        );
        queueItem.resolve(result);
      } catch (error) {
        queueItem.reject(error);
      }
    }

    this.isProcessingQueue = false;
    console.log('[NetworkManager] 离线队列处理完成');
  }

  /**
   * 清空离线队列
   */
  clearOfflineQueue() {
    const count = this.requestQueue.length;
    this.requestQueue.forEach(item => {
      item.reject(new Error('离线队列已清空'));
    });
    this.requestQueue = [];
    console.log('[NetworkManager] 离线队列已清空:', count);
  }

  /**
   * 添加请求拦截器
   * @param {Function} interceptor - 拦截器函数
   */
  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor);
  }

  /**
   * 添加响应拦截器
   * @param {Function} interceptor - 拦截器函数
   */
  addResponseInterceptor(interceptor) {
    this.responseInterceptors.push(interceptor);
  }

  /**
   * 应用请求拦截器
   * @param {Object} options - 请求选项
   */
  async applyRequestInterceptors(options) {
    let processedOptions = { ...options };
    
    for (const interceptor of this.requestInterceptors) {
      try {
        processedOptions = await interceptor(processedOptions);
      } catch (error) {
        console.error('[NetworkManager] 请求拦截器执行失败:', error);
      }
    }
    
    return processedOptions;
  }

  /**
   * 应用响应拦截器
   * @param {Object} response - 响应对象
   * @param {Object} options - 请求选项
   */
  async applyResponseInterceptors(response, options) {
    let processedResponse = response;
    
    for (const interceptor of this.responseInterceptors) {
      try {
        processedResponse = await interceptor(processedResponse, options);
      } catch (error) {
        console.error('[NetworkManager] 响应拦截器执行失败:', error);
      }
    }
    
    return processedResponse;
  }

  /**
   * GET 请求
   * @param {string} url - 请求地址
   * @param {Object} params - 请求参数
   * @param {Object} options - 其他选项
   */
  async get(url, params = {}, options = {}) {
    // 将参数拼接到URL
    const queryString = Object.keys(params)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
      .join('&');
    
    const fullUrl = queryString ? `${url}?${queryString}` : url;
    
    return this.request({
      url: fullUrl,
      method: 'GET',
      ...options
    });
  }

  /**
   * POST 请求
   * @param {string} url - 请求地址
   * @param {Object} data - 请求数据
   * @param {Object} options - 其他选项
   */
  async post(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'POST',
      data,
      ...options
    });
  }

  /**
   * PUT 请求
   * @param {string} url - 请求地址
   * @param {Object} data - 请求数据
   * @param {Object} options - 其他选项
   */
  async put(url, data = {}, options = {}) {
    return this.request({
      url,
      method: 'PUT',
      data,
      ...options
    });
  }

  /**
   * DELETE 请求
   * @param {string} url - 请求地址
   * @param {Object} options - 其他选项
   */
  async delete(url, options = {}) {
    return this.request({
      url,
      method: 'DELETE',
      ...options
    });
  }

  /**
   * 文件上传
   * @param {Object} options - 上传选项
   */
  async uploadFile(options) {
    const {
      url,
      filePath,
      name = 'file',
      formData = {},
      header = {},
      timeout = NETWORK_CONFIG.REQUEST_TIMEOUT
    } = options;

    if (this.isOffline()) {
      throw new Error('网络不可用，无法上传文件');
    }

    return new Promise((resolve, reject) => {
      const uploadTask = wx.uploadFile({
        url,
        filePath,
        name,
        formData,
        header,
        success: (res) => {
          console.log('[NetworkManager] 文件上传成功:', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('[NetworkManager] 文件上传失败:', err);
          reject(new Error(err.errMsg || '文件上传失败'));
        }
      });

      // 设置超时
      if (timeout) {
        setTimeout(() => {
          uploadTask.abort();
          reject(new Error('上传超时'));
        }, timeout);
      }
    });
  }

  /**
   * 文件下载
   * @param {Object} options - 下载选项
   */
  async downloadFile(options) {
    const {
      url,
      header = {},
      timeout = NETWORK_CONFIG.REQUEST_TIMEOUT
    } = options;

    if (this.isOffline()) {
      throw new Error('网络不可用，无法下载文件');
    }

    return new Promise((resolve, reject) => {
      const downloadTask = wx.downloadFile({
        url,
        header,
        success: (res) => {
          console.log('[NetworkManager] 文件下载成功:', res);
          resolve(res);
        },
        fail: (err) => {
          console.error('[NetworkManager] 文件下载失败:', err);
          reject(new Error(err.errMsg || '文件下载失败'));
        }
      });

      // 设置超时
      if (timeout) {
        setTimeout(() => {
          downloadTask.abort();
          reject(new Error('下载超时'));
        }, timeout);
      }
    });
  }

  /**
   * 延迟函数
   * @param {number} ms - 延迟毫秒数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 获取网络状态统计
   */
  getStats() {
    return {
      networkStatus: this.networkStatus,
      networkType: this.networkType,
      isOnline: this.isOnline(),
      isOffline: this.isOffline(),
      queueLength: this.requestQueue.length,
      isProcessingQueue: this.isProcessingQueue,
      requestInterceptorsCount: this.requestInterceptors.length,
      responseInterceptorsCount: this.responseInterceptors.length
    };
  }

  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * 移除事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('[NetworkManager] 事件回调执行失败:', error);
        }
      });
    }
  }
}

// 创建单例
const networkManager = new NetworkManager();

export default networkManager;
