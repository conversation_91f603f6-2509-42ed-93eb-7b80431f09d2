# 环境配置文件说明

本项目包含两个环境配置相关的文件，它们各有不同的用途和作用域。

## 📁 文件概览

### 1. `miniprogram/envList.js`
- **类型**: CommonJS 模块
- **来源**: 微信小程序云开发快速启动模板
- **用途**: 云开发环境列表配置，兼容微信官方模板
- **格式**: 简单的环境列表数组

### 2. `miniprogram/utils/config/env.js`
- **类型**: ES6 模块
- **来源**: 自定义企业级环境管理系统
- **用途**: 完整的应用环境配置管理
- **格式**: 复杂的多维度配置对象

## 🔗 两者关系

### 兼容性设计
- `envList.js` 现在会自动从 `env.js` 中读取环境配置
- 保持与微信官方模板的兼容性
- 避免重复配置，统一配置源

### 数据流向
```
env.js (主配置) → envList.js (兼容层) → 微信云开发API
```

## 📋 功能对比

| 特性 | envList.js | env.js |
|------|------------|--------|
| **主要用途** | 云开发环境列表 | 完整环境配置管理 |
| **配置范围** | 仅云环境ID | 云开发、API、日志、缓存等 |
| **环境支持** | 动态从env.js获取 | 开发/测试/预发布/生产 |
| **模块类型** | CommonJS | ES6 Module |
| **兼容性** | 微信官方模板 | 现代化配置管理 |
| **配置复杂度** | 简单 | 复杂 |

## 🚀 使用建议

### 1. 主要配置管理
**推荐使用 `env.js`** 进行所有环境配置管理：

```javascript
// 推荐方式
import EnvConfig from './utils/config/env.js';
const config = EnvConfig.getEnvConfig();
```

### 2. 云开发兼容
如果需要与微信官方模板兼容，可以使用 `envList.js`：

```javascript
// 兼容方式
const { envList, getCurrentEnvConfig } = require('./envList.js');
```

### 3. 配置修改
**只需要修改 `env.js`**，`envList.js` 会自动同步：

```javascript
// 在 env.js 中修改配置
const ENV_CONFIGS = {
  [ENV_TYPES.DEVELOPMENT]: {
    cloud: {
      env: 'your-dev-env-id', // 修改这里
    }
  }
};
```

## ⚙️ 配置流程

### 1. 初始配置
1. 编辑 `miniprogram/utils/config/env.js`
2. 更新各环境的云环境ID、API地址等
3. `envList.js` 会自动获取这些配置

### 2. 环境识别
```javascript
// env.js 中的自动环境识别
try {
  const accountInfo = wx.getAccountInfoSync();
  const appId = accountInfo.miniProgram.appId;
  
  // 根据 appId 判断环境
  if (appId === 'your-dev-appid') {
    CURRENT_ENV = ENV_TYPES.DEVELOPMENT;
  }
} catch (error) {
  // 默认开发环境
}
```

### 3. 配置使用
```javascript
// 在应用中使用
const app = getApp();
const envConfig = app.getEnvConfig();
const cloudEnv = envConfig.cloud.env;
```

## 🔧 迁移指南

### 从 envList.js 迁移到 env.js

如果你之前使用 `envList.js` 进行配置：

```javascript
// 旧方式 (envList.js)
const envList = [
  { alias: '开发环境', envId: 'dev-env-id' },
  { alias: '生产环境', envId: 'prod-env-id' }
];

// 新方式 (env.js)
const ENV_CONFIGS = {
  [ENV_TYPES.DEVELOPMENT]: {
    name: '开发环境',
    cloud: { env: 'dev-env-id' }
  },
  [ENV_TYPES.PRODUCTION]: {
    name: '生产环境',
    cloud: { env: 'prod-env-id' }
  }
};
```

## 🎯 最佳实践

### 1. 统一配置源
- 所有环境配置都在 `env.js` 中管理
- 不要直接修改 `envList.js`
- 让 `envList.js` 自动从 `env.js` 获取配置

### 2. 环境隔离
- 不同环境使用不同的云环境ID
- API地址、日志级别等都要区分环境
- 生产环境关闭调试功能

### 3. 配置验证
```javascript
// 在应用启动时验证配置
const envConfig = EnvConfig.getEnvConfig();
if (!envConfig.cloud.env) {
  console.error('云环境ID未配置');
}
```

### 4. 敏感信息处理
- 不要在代码中硬编码敏感信息
- 使用环境变量或安全的配置管理
- 生产环境配置要特别小心

## 🐛 常见问题

### Q: 为什么需要两个环境配置文件？
A: `envList.js` 是为了兼容微信官方模板，`env.js` 提供更完整的配置管理。现在它们已经整合，避免重复配置。

### Q: 我应该修改哪个文件？
A: 只需要修改 `env.js`，`envList.js` 会自动同步配置。

### Q: 如何添加新的云环境？
A: 在 `env.js` 中添加新的环境配置，`envList.js` 会自动包含新环境。

### Q: 配置修改后需要重启吗？
A: 是的，环境配置在应用启动时加载，修改后需要重启小程序。

## 📞 技术支持

如果在使用过程中遇到问题：

1. 检查控制台是否有错误信息
2. 确认云环境ID是否正确
3. 验证网络连接和权限设置
4. 查看微信开发者工具的调试信息

---

**总结**: 现在你有了一个既兼容微信官方模板，又提供企业级配置管理的完整解决方案。只需要在 `env.js` 中进行配置，其他文件会自动同步，避免了重复配置的问题。
