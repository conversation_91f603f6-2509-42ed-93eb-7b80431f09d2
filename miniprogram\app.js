// app.js
import GlobalManager, {
  CloudManager,
  CacheManager,
  LogManager,
  UserManager,
  NetworkManager
} from './utils/managers/GlobalManager.js';
import { LOG_LEVELS, EnvConfig, ENV_UTILS } from './utils/constants/config.js';

App({
  onLaunch: async function () {
    console.log('[App] 小程序启动');

    // 获取当前环境配置
    const envConfig = EnvConfig.getEnvConfig();
    const envSummary = EnvConfig.getEnvSummary();

    console.log('[App] 当前环境:', envSummary);

    // 初始化全局数据
    this.globalData = {
      // 环境配置
      env: envConfig.cloud.env,
      envConfig: envConfig,
      envSummary: envSummary,
      // 全局管理器实例
      globalManager: GlobalManager,
      // 各个管理器的快捷访问
      managers: {
        cloud: CloudManager,
        cache: CacheManager,
        log: Log<PERSON>anager,
        user: UserManager,
        network: NetworkManager
      }
    };

    try {
      // 初始化全局管理器（使用环境配置）
      const initResults = await GlobalManager.init({
        // 日志配置（从环境配置获取）
        log: {
          level: LOG_LEVELS[envConfig.log.level] || LOG_LEVELS.INFO,
          maxLogCount: envConfig.log.maxLogCount,
          enableUpload: envConfig.log.enableUpload,
          uploadUrl: envConfig.log.uploadUrl,
          uploadInterval: envConfig.log.uploadInterval,
          enableConsole: envConfig.log.enableConsole
        },
        // 云服务配置（从环境配置获取）
        cloud: {
          env: envConfig.cloud.env,
          timeout: envConfig.cloud.timeout,
          traceUser: envConfig.cloud.traceUser
        },
        // 缓存配置（从环境配置获取）
        cache: envConfig.cache,
        // 网络配置（从环境配置获取）
        network: envConfig.api,
        // 用户配置（从环境配置获取）
        user: envConfig.user
      });

      console.log('[App] 全局管理器初始化完成:', initResults);

      // 设置全局事件监听
      this.setupGlobalEventListeners();

      // 兼容旧版云开发初始化（保持向后兼容）
      this.initLegacyCloud();

    } catch (error) {
      console.error('[App] 全局管理器初始化失败:', error);
      // 即使初始化失败，也要保证应用能正常启动
      this.initLegacyCloud();
    }
  },

  onShow: function () {
    console.log('[App] 小程序显示');
    // GlobalManager 会自动处理 onShow 事件
  },

  onHide: function () {
    console.log('[App] 小程序隐藏');
    // GlobalManager 会自动处理 onHide 事件
  },

  onError: function (error) {
    console.error('[App] 小程序错误:', error);
    // 错误会被 GlobalManager 自动捕获和记录
  },

  /**
   * 设置全局事件监听
   */
  setupGlobalEventListeners: function () {
    const globalManager = this.globalData.globalManager;

    // 监听网络状态变化
    globalManager.on('network_restored', (data) => {
      console.log('[App] 网络已恢复:', data);
      // 可以在这里添加网络恢复后的处理逻辑
    });

    globalManager.on('network_lost', (data) => {
      console.log('[App] 网络已断开:', data);
      // 可以在这里添加网络断开后的处理逻辑
    });

    // 监听用户登录状态变化
    globalManager.on('user_logged_in', (data) => {
      console.log('[App] 用户已登录:', data);
      // 可以在这里添加用户登录后的处理逻辑
    });

    globalManager.on('user_logged_out', (data) => {
      console.log('[App] 用户已登出:', data);
      // 可以在这里添加用户登出后的处理逻辑
    });

    // 监听错误事件
    globalManager.on('error_occurred', (data) => {
      console.log('[App] 发生错误:', data);
      // 可以在这里添加错误处理逻辑，比如错误上报
    });
  },

  /**
   * 兼容旧版云开发初始化
   */
  initLegacyCloud: function () {
    if (!wx.cloud) {
      console.error("请使用 2.2.3 或以上的基础库以使用云能力");
      return;
    }

    // 如果 GlobalManager 初始化失败，使用传统方式初始化云开发
    if (!this.globalData.globalManager.isReady()) {
      wx.cloud.init({
        env: this.globalData.env,
        traceUser: true,
      });
      console.log('[App] 使用传统方式初始化云开发');
    }
  },

  /**
   * 获取全局管理器
   */
  getGlobalManager: function () {
    return this.globalData.globalManager;
  },

  /**
   * 获取指定管理器
   * @param {string} name - 管理器名称 (cloud, cache, log, user, network)
   */
  getManager: function (name) {
    return this.globalData.managers[name];
  },

  /**
   * 获取系统状态
   */
  getSystemStatus: function () {
    return this.globalData.globalManager.getSystemStatus();
  },

  /**
   * 获取环境配置
   */
  getEnvConfig: function () {
    return this.globalData.envConfig;
  },

  /**
   * 获取环境摘要
   */
  getEnvSummary: function () {
    return this.globalData.envSummary;
  },

  /**
   * 检查是否为调试模式
   */
  isDebug: function () {
    return this.globalData.envConfig.debug;
  },

  /**
   * 检查当前环境类型
   */
  isDevelopment: function () {
    return ENV_UTILS.isDevelopment();
  },

  isTesting: function () {
    return ENV_UTILS.isTesting();
  },

  isStaging: function () {
    return ENV_UTILS.isStaging();
  },

  isProduction: function () {
    return ENV_UTILS.isProduction();
  }
});
