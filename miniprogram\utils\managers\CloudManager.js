/**
 * 云服务管理器
 * 统一管理微信云开发相关功能
 */

import { CLOUD_CONFIG } from '../constants/config.js';

class CloudManager {
  constructor() {
    this.isInitialized = false;
    this.env = '';
    this.db = null;
  }

  /**
   * 初始化云服务
   * @param {string} env - 云环境ID
   */
  init(env = '') {
    try {
      if (!wx.cloud) {
        throw new Error('云开发功能不可用，请检查基础库版本');
      }

      wx.cloud.init({
        env: env,
        traceUser: true
      });

      this.env = env;
      this.db = wx.cloud.database();
      this.isInitialized = true;
      
      console.log('[CloudManager] 云服务初始化成功', { env });
      return true;
    } catch (error) {
      console.error('[CloudManager] 云服务初始化失败:', error);
      return false;
    }
  }

  /**
   * 检查云服务是否已初始化
   */
  checkInitialized() {
    if (!this.isInitialized) {
      throw new Error('云服务未初始化，请先调用 init() 方法');
    }
  }

  /**
   * 调用云函数
   * @param {string} name - 云函数名称
   * @param {Object} data - 传递的数据
   * @param {number} timeout - 超时时间
   */
  async callFunction(name, data = {}, timeout = CLOUD_CONFIG.FUNCTION_TIMEOUT) {
    this.checkInitialized();
    
    try {
      const result = await wx.cloud.callFunction({
        name,
        data,
        timeout
      });
      
      console.log(`[CloudManager] 云函数 ${name} 调用成功:`, result);
      return {
        success: true,
        data: result.result,
        requestId: result.requestId
      };
    } catch (error) {
      console.error(`[CloudManager] 云函数 ${name} 调用失败:`, error);
      return {
        success: false,
        error: error.message || '云函数调用失败',
        code: error.errCode
      };
    }
  }

  /**
   * 数据库查询
   * @param {string} collection - 集合名称
   * @param {Object} where - 查询条件
   * @param {number} limit - 查询限制
   */
  async dbGet(collection, where = {}, limit = CLOUD_CONFIG.DB_QUERY_LIMIT) {
    this.checkInitialized();
    
    try {
      let query = this.db.collection(collection);
      
      // 添加查询条件
      if (Object.keys(where).length > 0) {
        query = query.where(where);
      }
      
      const result = await query.limit(limit).get();
      
      console.log(`[CloudManager] 数据库查询成功 ${collection}:`, result);
      return {
        success: true,
        data: result.data,
        total: result.data.length
      };
    } catch (error) {
      console.error(`[CloudManager] 数据库查询失败 ${collection}:`, error);
      return {
        success: false,
        error: error.message || '数据库查询失败',
        code: error.errCode
      };
    }
  }

  /**
   * 数据库添加
   * @param {string} collection - 集合名称
   * @param {Object} data - 要添加的数据
   */
  async dbAdd(collection, data) {
    this.checkInitialized();
    
    try {
      const result = await this.db.collection(collection).add({
        data: {
          ...data,
          createTime: new Date(),
          updateTime: new Date()
        }
      });
      
      console.log(`[CloudManager] 数据库添加成功 ${collection}:`, result);
      return {
        success: true,
        id: result._id
      };
    } catch (error) {
      console.error(`[CloudManager] 数据库添加失败 ${collection}:`, error);
      return {
        success: false,
        error: error.message || '数据库添加失败',
        code: error.errCode
      };
    }
  }

  /**
   * 数据库更新
   * @param {string} collection - 集合名称
   * @param {string} id - 文档ID
   * @param {Object} data - 要更新的数据
   */
  async dbUpdate(collection, id, data) {
    this.checkInitialized();
    
    try {
      const result = await this.db.collection(collection).doc(id).update({
        data: {
          ...data,
          updateTime: new Date()
        }
      });
      
      console.log(`[CloudManager] 数据库更新成功 ${collection}:`, result);
      return {
        success: true,
        updated: result.stats.updated
      };
    } catch (error) {
      console.error(`[CloudManager] 数据库更新失败 ${collection}:`, error);
      return {
        success: false,
        error: error.message || '数据库更新失败',
        code: error.errCode
      };
    }
  }

  /**
   * 数据库删除
   * @param {string} collection - 集合名称
   * @param {string} id - 文档ID
   */
  async dbRemove(collection, id) {
    this.checkInitialized();
    
    try {
      const result = await this.db.collection(collection).doc(id).remove();
      
      console.log(`[CloudManager] 数据库删除成功 ${collection}:`, result);
      return {
        success: true,
        removed: result.stats.removed
      };
    } catch (error) {
      console.error(`[CloudManager] 数据库删除失败 ${collection}:`, error);
      return {
        success: false,
        error: error.message || '数据库删除失败',
        code: error.errCode
      };
    }
  }

  /**
   * 文件上传
   * @param {string} cloudPath - 云端路径
   * @param {string} filePath - 本地文件路径
   */
  async uploadFile(cloudPath, filePath) {
    this.checkInitialized();
    
    try {
      const result = await wx.cloud.uploadFile({
        cloudPath,
        filePath
      });
      
      console.log('[CloudManager] 文件上传成功:', result);
      return {
        success: true,
        fileID: result.fileID
      };
    } catch (error) {
      console.error('[CloudManager] 文件上传失败:', error);
      return {
        success: false,
        error: error.message || '文件上传失败',
        code: error.errCode
      };
    }
  }

  /**
   * 文件下载
   * @param {string} fileID - 文件ID
   */
  async downloadFile(fileID) {
    this.checkInitialized();
    
    try {
      const result = await wx.cloud.downloadFile({
        fileID
      });
      
      console.log('[CloudManager] 文件下载成功:', result);
      return {
        success: true,
        tempFilePath: result.tempFilePath
      };
    } catch (error) {
      console.error('[CloudManager] 文件下载失败:', error);
      return {
        success: false,
        error: error.message || '文件下载失败',
        code: error.errCode
      };
    }
  }

  /**
   * 文件删除
   * @param {Array} fileList - 文件ID列表
   */
  async deleteFile(fileList) {
    this.checkInitialized();
    
    try {
      const result = await wx.cloud.deleteFile({
        fileList
      });
      
      console.log('[CloudManager] 文件删除成功:', result);
      return {
        success: true,
        fileList: result.fileList
      };
    } catch (error) {
      console.error('[CloudManager] 文件删除失败:', error);
      return {
        success: false,
        error: error.message || '文件删除失败',
        code: error.errCode
      };
    }
  }

  /**
   * 获取临时链接
   * @param {Array} fileList - 文件ID列表
   */
  async getTempFileURL(fileList) {
    this.checkInitialized();
    
    try {
      const result = await wx.cloud.getTempFileURL({
        fileList
      });
      
      console.log('[CloudManager] 获取临时链接成功:', result);
      return {
        success: true,
        fileList: result.fileList
      };
    } catch (error) {
      console.error('[CloudManager] 获取临时链接失败:', error);
      return {
        success: false,
        error: error.message || '获取临时链接失败',
        code: error.errCode
      };
    }
  }
}

// 创建单例
const cloudManager = new CloudManager();

export default cloudManager;
