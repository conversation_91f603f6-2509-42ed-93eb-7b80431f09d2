# 环境配置管理

本项目提供了完整的环境配置管理系统，支持开发、测试、预发布、生产四种环境的统一配置管理。

## 🚀 特性

- **多环境支持**: 开发、测试、预发布、生产环境
- **自动环境识别**: 基于小程序 appId 自动识别当前环境
- **统一配置管理**: 云服务、API、日志、缓存等配置统一管理
- **类型安全**: 完整的 TypeScript 类型定义
- **便捷切换**: 开发环境下支持动态切换环境（需重启生效）

## 📁 文件结构

```
miniprogram/utils/config/
├── env.js              # 环境配置主文件
└── README.md          # 说明文档

miniprogram/utils/constants/
└── config.js          # 全局配置常量（集成环境配置）
```

## 🔧 配置说明

### 环境类型

- `development`: 开发环境
- `testing`: 测试环境  
- `staging`: 预发布环境
- `production`: 生产环境

### 配置项说明

每个环境包含以下配置项：

#### 基本配置
- `name`: 环境名称
- `debug`: 是否开启调试模式

#### 云开发配置 (`cloud`)
- `env`: 云环境 ID
- `timeout`: 云函数超时时间
- `traceUser`: 是否追踪用户

#### API 配置 (`api`)
- `baseUrl`: API 基础地址
- `timeout`: 请求超时时间
- `retryCount`: 重试次数
- `retryInterval`: 重试间隔

#### 日志配置 (`log`)
- `level`: 日志级别 (DEBUG/INFO/WARN/ERROR)
- `enableConsole`: 是否启用控制台输出
- `enableUpload`: 是否启用日志上报
- `uploadUrl`: 日志上报地址
- `maxLogCount`: 最大日志数量
- `uploadInterval`: 上报间隔

#### 缓存配置 (`cache`)
- `defaultExpireTime`: 默认过期时间
- `maxCacheSize`: 最大缓存大小
- `enablePersistent`: 是否启用持久化

#### 用户配置 (`user`)
- `autoLogin`: 是否自动登录
- `tokenExpireTime`: Token 过期时间
- `enableGuestMode`: 是否启用游客模式

#### 功能开关 (`features`)
- `enableAnalytics`: 是否启用统计分析
- `enableCrashReport`: 是否启用崩溃上报
- `enablePerformanceMonitor`: 是否启用性能监控
- `enableDebugPanel`: 是否启用调试面板

#### 第三方服务配置 (`thirdParty`)
- `analytics`: 统计分析配置
- `payment`: 支付配置

## 📖 使用方法

### 1. 基本使用

```javascript
import EnvConfig from './utils/config/env.js';

// 获取当前环境配置
const envConfig = EnvConfig.getEnvConfig();
console.log('当前环境:', envConfig.name);

// 获取特定配置
const cloudEnv = EnvConfig.getConfig('cloud').env;
const apiBaseUrl = EnvConfig.getConfig('api').baseUrl;
```

### 2. 环境判断

```javascript
import { ENV_UTILS } from './utils/constants/config.js';

// 判断当前环境
if (ENV_UTILS.isDevelopment()) {
  console.log('当前是开发环境');
}

if (ENV_UTILS.isProduction()) {
  console.log('当前是生产环境');
}

// 判断是否为调试模式
if (ENV_UTILS.isDebug()) {
  console.log('当前是调试模式');
}
```

### 3. 在 App 中使用

```javascript
// app.js
const app = getApp();

// 获取环境配置
const envConfig = app.getEnvConfig();
const envSummary = app.getEnvSummary();

// 环境判断
const isDebug = app.isDebug();
const isDev = app.isDevelopment();
```

### 4. 在页面中使用

```javascript
// pages/xxx/xxx.js
const app = getApp();

Page({
  onLoad() {
    // 获取环境信息
    const envInfo = app.getEnvSummary();
    console.log('环境信息:', envInfo);
    
    // 根据环境执行不同逻辑
    if (app.isDevelopment()) {
      // 开发环境特有逻辑
    }
  }
});
```

## ⚙️ 配置自定义

### 1. 修改环境配置

编辑 `miniprogram/utils/config/env.js` 文件中的 `ENV_CONFIGS` 对象：

```javascript
const ENV_CONFIGS = {
  [ENV_TYPES.DEVELOPMENT]: {
    name: '开发环境',
    cloud: {
      env: 'your-dev-env-id', // 修改为你的云环境ID
    },
    api: {
      baseUrl: 'https://your-dev-api.com', // 修改为你的API地址
    },
    // ... 其他配置
  },
  // ... 其他环境
};
```

### 2. 自动环境识别

修改 `env.js` 文件中的环境识别逻辑：

```javascript
try {
  const accountInfo = wx.getAccountInfoSync();
  const appId = accountInfo.miniProgram.appId;
  
  // 根据 appId 判断环境
  switch (appId) {
    case 'wx1234567890abcdef': // 开发环境 appId
      CURRENT_ENV = ENV_TYPES.DEVELOPMENT;
      break;
    case 'wx0987654321fedcba': // 生产环境 appId
      CURRENT_ENV = ENV_TYPES.PRODUCTION;
      break;
    default:
      CURRENT_ENV = ENV_TYPES.DEVELOPMENT;
  }
} catch (error) {
  console.warn('[EnvConfig] 无法获取账号信息，使用默认环境');
}
```

### 3. 添加新的配置项

在 `ENV_CONFIGS` 中添加新的配置项：

```javascript
const ENV_CONFIGS = {
  [ENV_TYPES.DEVELOPMENT]: {
    // 现有配置...
    
    // 新增配置
    customConfig: {
      feature1: true,
      feature2: 'dev-value'
    }
  }
};
```

## 🔄 环境切换

### 开发环境下动态切换

在开发环境下，可以通过以下方式动态切换环境：

```javascript
import EnvConfig from './utils/config/env.js';

// 切换到测试环境
EnvConfig.setCurrentEnv('testing');

// 注意：切换后需要重启小程序才能生效
```

### 构建时环境设置

可以通过构建工具在编译时设置环境：

```javascript
// 在构建脚本中注入环境变量
const env = process.env.NODE_ENV || 'development';
// 然后在代码中使用这个环境变量
```

## 📝 最佳实践

1. **环境隔离**: 不同环境使用不同的云环境 ID 和 API 地址
2. **配置验证**: 在应用启动时验证关键配置项是否正确
3. **敏感信息**: 敏感配置（如密钥）不要直接写在代码中
4. **版本管理**: 配置文件纳入版本管理，但敏感信息要排除
5. **文档更新**: 配置变更时及时更新文档

## 🐛 常见问题

### Q: 如何在生产环境禁用调试功能？
A: 在生产环境配置中设置 `debug: false` 和 `features.enableDebugPanel: false`

### Q: 如何添加新的环境？
A: 在 `ENV_TYPES` 中添加新的环境类型，然后在 `ENV_CONFIGS` 中添加对应配置

### Q: 环境切换后为什么没有生效？
A: 环境切换后需要重启小程序才能生效，因为配置在应用启动时加载

### Q: 如何在不同环境使用不同的云函数？
A: 在不同环境的云环境中部署相同名称的云函数，通过云环境 ID 自动路由

## 📞 技术支持

如有问题，请查看：
1. 控制台错误信息
2. 网络请求状态
3. 云开发控制台日志
4. 小程序开发者工具调试信息
