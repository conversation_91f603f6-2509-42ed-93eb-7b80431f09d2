/* pages/products/products.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  background-color: #fff;
  padding: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.search-input-wrapper {
  position: relative;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 40rpx;
}

.search-input {
  width: 100%;
  font-size: 28rpx;
  color: #333;
}

.search-icon {
  position: absolute;
  right: 30rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #999;
}

/* 分类导航 */
.category-nav {
  background-color: #fff;
  border-bottom: 1rpx solid #eee;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: inline-flex;
  padding: 20rpx;
}

.category-item {
  padding: 16rpx 32rpx;
  margin-right: 20rpx;
  background-color: #f5f5f5;
  border-radius: 40rpx;
  font-size: 28rpx;
  color: #666;
  white-space: nowrap;
  transition: all 0.3s ease;
}

.category-item.active {
  background-color: #3cc51f;
  color: #fff;
}

/* 产品列表 */
.products-section {
  padding: 20rpx;
}

.empty-state {
  text-align: center;
  padding: 120rpx 0;
  color: #999;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.product-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.product-card:active {
  transform: scale(0.98);
}

.product-image-wrapper {
  width: 100%;
  height: 300rpx;
  overflow: hidden;
}

.product-image {
  width: 100%;
  height: 100%;
}

.product-info {
  padding: 24rpx;
}

.product-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-description {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 16rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-price {
  font-size: 36rpx;
  font-weight: 700;
  color: #ff4757;
}

/* 响应式设计 */
@media (max-width: 750rpx) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1200rpx) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}
