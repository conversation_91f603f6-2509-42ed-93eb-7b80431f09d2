// pages/products/products.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    searchValue: '',
    categories: [
      { id: 'all', name: '全部' },
      { id: 'electronics', name: '电子产品' },
      { id: 'clothing', name: '服装' },
      { id: 'home', name: '家居' },
      { id: 'beauty', name: '美妆' }
    ],
    activeCategory: 'all',
    products: [
      {
        id: 1,
        name: 'UW智能手表',
        price: 1299,
        image: '/images/default-goods-image.png',
        category: 'electronics',
        description: '智能健康监测，时尚外观设计'
      },
      {
        id: 2,
        name: 'UW运动T恤',
        price: 199,
        image: '/images/default-goods-image.png',
        category: 'clothing',
        description: '舒适透气，运动首选'
      },
      {
        id: 3,
        name: 'UW香薰机',
        price: 299,
        image: '/images/default-goods-image.png',
        category: 'home',
        description: '营造温馨氛围，提升生活品质'
      }
    ],
    filteredProducts: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.setData({
      filteredProducts: this.data.products
    });
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const value = e.detail.value;
    this.setData({
      searchValue: value
    });
    this.filterProducts();
  },

  /**
   * 分类切换
   */
  onCategoryTap(e) {
    const category = e.currentTarget.dataset.category;
    this.setData({
      activeCategory: category
    });
    this.filterProducts();
  },

  /**
   * 过滤产品
   */
  filterProducts() {
    let filtered = this.data.products;
    
    // 按分类过滤
    if (this.data.activeCategory !== 'all') {
      filtered = filtered.filter(product => product.category === this.data.activeCategory);
    }
    
    // 按搜索关键词过滤
    if (this.data.searchValue) {
      filtered = filtered.filter(product => 
        product.name.toLowerCase().includes(this.data.searchValue.toLowerCase()) ||
        product.description.toLowerCase().includes(this.data.searchValue.toLowerCase())
      );
    }
    
    this.setData({
      filteredProducts: filtered
    });
  },

  /**
   * 产品点击
   */
  onProductTap(e) {
    const productId = e.currentTarget.dataset.id;
    wx.showToast({
      title: `查看产品 ${productId}`,
      icon: 'none'
    });
    // TODO: 跳转到产品详情页
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉刷新
   */
  onPullDownRefresh() {
    // 模拟刷新数据
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {
    wx.showToast({
      title: '加载更多产品',
      icon: 'none'
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'UW产品中心',
      path: '/pages/products/products'
    };
  }
});
