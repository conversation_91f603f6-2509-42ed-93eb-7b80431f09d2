<!--pages/feedback/feedback.wxml-->
<view class="container">
  <!-- 标签页导航 -->
  <view class="tab-nav">
    <view 
      class="tab-item {{activeTab === 'submit' ? 'active' : ''}}"
      data-tab="submit"
      bindtap="onTabChange"
    >
      提交反馈
    </view>
    <view 
      class="tab-item {{activeTab === 'history' ? 'active' : ''}}"
      data-tab="history"
      bindtap="onTabChange"
    >
      历史反馈
    </view>
  </view>

  <!-- 提交反馈页面 -->
  <view wx:if="{{activeTab === 'submit'}}" class="submit-feedback">
    <view class="form-section">
      <!-- 反馈类型 -->
      <view class="form-item">
        <view class="form-label">反馈类型</view>
        <picker 
          range="{{feedbackTypes}}" 
          range-key="label"
          value="{{feedbackForm.type}}"
          bindchange="onTypeChange"
        >
          <view class="picker-display">
            <view wx:for="{{feedbackTypes}}" wx:key="value" wx:if="{{item.value === feedbackForm.type}}">
              <text class="type-icon">{{item.icon}}</text>
              <text class="type-label">{{item.label}}</text>
            </view>
            <view class="picker-arrow">></view>
          </view>
        </picker>
      </view>

      <!-- 反馈标题 -->
      <view class="form-item">
        <view class="form-label">反馈标题</view>
        <input 
          class="form-input"
          placeholder="请简要描述问题或建议"
          value="{{feedbackForm.title}}"
          bindinput="onTitleInput"
          maxlength="50"
        />
      </view>

      <!-- 详细描述 -->
      <view class="form-item">
        <view class="form-label">详细描述</view>
        <textarea 
          class="form-textarea"
          placeholder="请详细描述您遇到的问题或建议，我们会认真对待每一条反馈"
          value="{{feedbackForm.content}}"
          bindinput="onContentInput"
          maxlength="500"
          show-confirm-bar="{{false}}"
        />
        <view class="char-count">{{feedbackForm.content.length}}/500</view>
      </view>

      <!-- 图片上传 -->
      <view class="form-item">
        <view class="form-label">相关图片（可选）</view>
        <view class="image-upload">
          <view 
            wx:for="{{feedbackForm.images}}" 
            wx:key="*this"
            class="image-item"
          >
            <image 
              src="{{item}}" 
              mode="aspectFill"
              bindtap="onPreviewImage"
              data-src="{{item}}"
            />
            <view 
              class="delete-btn"
              data-index="{{index}}"
              bindtap="onDeleteImage"
            >×</view>
          </view>
          <view 
            wx:if="{{feedbackForm.images.length < 3}}"
            class="upload-btn"
            bindtap="onChooseImage"
          >
            <view class="upload-icon">+</view>
            <view class="upload-text">添加图片</view>
          </view>
        </view>
        <view class="upload-tip">最多上传3张图片，支持jpg、png格式</view>
      </view>

      <!-- 联系方式 -->
      <view class="form-item">
        <view class="form-label">联系方式（可选）</view>
        <input 
          class="form-input"
          placeholder="请留下您的联系方式，方便我们回复"
          value="{{feedbackForm.contact}}"
          bindinput="onContactInput"
        />
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <button 
        class="submit-btn"
        bindtap="onSubmitFeedback"
      >
        提交反馈
      </button>
    </view>
  </view>

  <!-- 历史反馈页面 -->
  <view wx:if="{{activeTab === 'history'}}" class="feedback-history">
    <view wx:if="{{feedbackHistory.length === 0}}" class="empty-state">
      <view class="empty-icon">📝</view>
      <view class="empty-text">暂无反馈记录</view>
    </view>
    
    <view wx:else class="history-list">
      <view 
        wx:for="{{feedbackHistory}}" 
        wx:key="id"
        class="history-item"
        data-id="{{item.id}}"
        bindtap="onViewFeedback"
      >
        <view class="history-header">
          <view class="history-title">{{item.title}}</view>
          <view class="status-badge status-{{item.status}}">
            <text wx:if="{{item.status === 'pending'}}">待处理</text>
            <text wx:if="{{item.status === 'processing'}}">处理中</text>
            <text wx:if="{{item.status === 'resolved'}}">已解决</text>
          </view>
        </view>
        <view class="history-content">{{item.content}}</view>
        <view class="history-footer">
          <view class="history-time">{{item.createTime}}</view>
          <view class="history-type">
            <text wx:for="{{feedbackTypes}}" wx:key="value" wx:if="{{item.type === item.value}}">
              {{item.icon}} {{item.label}}
            </text>
          </view>
        </view>
        <view wx:if="{{item.reply}}" class="reply-section">
          <view class="reply-label">官方回复：</view>
          <view class="reply-content">{{item.reply}}</view>
        </view>
      </view>
    </view>
  </view>
</view>
