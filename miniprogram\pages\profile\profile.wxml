<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view wx:if="{{isLoggedIn}}" class="user-info">
      <view class="avatar-wrapper">
        <image 
          class="avatar" 
          src="{{userInfo.avatarUrl || '/images/icons/avatar.png'}}" 
          mode="aspectFill"
        />
      </view>
      <view class="user-details">
        <view class="username">{{userInfo.nickName || '用户'}}</view>
        <view class="user-desc">欢迎使用UW小程序</view>
      </view>
      <view class="user-actions">
        <button class="action-btn" bindtap="onGetUserProfile">更新信息</button>
        <button class="logout-btn" bindtap="onLogout">退出</button>
      </view>
    </view>
    
    <view wx:else class="login-prompt">
      <view class="login-avatar">
        <image class="avatar" src="/images/icons/avatar.png" mode="aspectFill" />
      </view>
      <view class="login-text">
        <view class="login-title">登录后享受更多服务</view>
        <view class="login-desc">查看订单、收藏商品、个性化推荐</view>
      </view>
      <button class="login-btn" bindtap="onLogin">立即登录</button>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <view class="section-title">我的服务</view>
    <view class="menu-list">
      <view 
        wx:for="{{menuItems}}" 
        wx:key="id"
        class="menu-item"
        data-id="{{item.id}}"
        bindtap="onMenuItemTap"
      >
        <view class="menu-icon">{{item.icon}}</view>
        <view class="menu-title">{{item.title}}</view>
        <view class="menu-extra">
          <view wx:if="{{item.badge > 0}}" class="badge">{{item.badge}}</view>
          <view wx:if="{{item.arrow}}" class="arrow">></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 设置菜单 -->
  <view class="menu-section">
    <view class="section-title">设置</view>
    <view class="menu-list">
      <view 
        wx:for="{{settingItems}}" 
        wx:key="id"
        class="menu-item"
        data-id="{{item.id}}"
        bindtap="onSettingItemTap"
      >
        <view class="menu-icon">{{item.icon}}</view>
        <view class="menu-title">{{item.title}}</view>
        <view class="menu-extra">
          <switch 
            wx:if="{{item.type === 'switch'}}"
            checked="{{item.value}}"
            bindchange="onNotificationSwitch"
            color="#3cc51f"
          />
          <view wx:elif="{{item.arrow}}" class="arrow">></view>
        </view>
      </view>
    </view>
  </view>

  <!-- 环境信息（开发模式） -->
  <view wx:if="{{showEnvInfo}}" class="env-section">
    <view class="section-title">开发信息</view>
    <view class="env-info" bindtap="onEnvInfoTap">
      <view class="env-item">
        <text class="env-label">当前环境:</text>
        <text class="env-value">{{envInfo.name}}</text>
      </view>
      <view class="env-item">
        <text class="env-label">环境类型:</text>
        <text class="env-value">{{envInfo.type}}</text>
      </view>
      <view class="env-item">
        <text class="env-label">调试模式:</text>
        <text class="env-value {{envInfo.debug ? 'debug-on' : 'debug-off'}}">
          {{envInfo.debug ? '开启' : '关闭'}}
        </text>
      </view>
      <view class="env-tip">点击查看详细信息</view>
    </view>
  </view>

  <!-- 版权信息 -->
  <view class="footer">
    <view class="copyright">© 2024 UW小程序 版权所有</view>
    <view class="version">v1.0.0</view>
  </view>
</view>
