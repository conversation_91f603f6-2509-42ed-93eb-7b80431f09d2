/**
 * 环境配置管理
 * 统一管理不同环境下的配置信息
 */

// 环境类型
export const ENV_TYPES = {
  DEVELOPMENT: 'development',
  TESTING: 'testing',
  STAGING: 'staging',
  PRODUCTION: 'production'
};

// 当前环境（可以通过构建工具或其他方式动态设置）
// 默认为开发环境，实际项目中可以通过以下方式确定：
// 1. 通过构建工具注入环境变量
// 2. 通过小程序账号信息判断
// 3. 通过域名或其他标识判断
let CURRENT_ENV = ENV_TYPES.DEVELOPMENT;

// 尝试从小程序账号信息判断环境
try {
  const accountInfo = wx.getAccountInfoSync();
  const appId = accountInfo.miniProgram.appId;
  
  // 根据不同的 appId 判断环境
  if (appId === 'wxf57755081128c53d') {
    // 这是示例项目的 appId，实际使用时需要替换
    CURRENT_ENV = ENV_TYPES.DEVELOPMENT;
  }
  // 可以添加更多 appId 判断逻辑
} catch (error) {
  console.warn('[EnvConfig] 无法获取账号信息，使用默认环境');
}

// 环境配置
const ENV_CONFIGS = {
  [ENV_TYPES.DEVELOPMENT]: {
    // 基本信息
    name: '开发环境',
    debug: true,
    
    // 云开发配置
    cloud: {
      env: 'cloud1-9g2uk6ls66dcea22', // 开发环境云环境ID
      timeout: 15000,
      traceUser: true
    },
    
    // API 配置
    api: {
      baseUrl: 'https://dev-api.example.com',
      timeout: 10000,
      retryCount: 3,
      retryInterval: 1000
    },
    
    // 日志配置
    log: {
      level: 'DEBUG',
      enableConsole: true,
      enableUpload: false,
      uploadUrl: '',
      maxLogCount: 1000,
      uploadInterval: 60000
    },
    
    // 缓存配置
    cache: {
      defaultExpireTime: 30 * 60 * 1000, // 30分钟
      maxCacheSize: 100,
      enablePersistent: true
    },
    
    // 用户配置
    user: {
      autoLogin: true,
      tokenExpireTime: 7 * 24 * 60 * 60 * 1000, // 7天
      enableGuestMode: true
    },
    
    // 功能开关
    features: {
      enableAnalytics: false,
      enableCrashReport: false,
      enablePerformanceMonitor: false,
      enableDebugPanel: true
    },
    
    // 第三方服务配置
    thirdParty: {
      analytics: {
        appKey: 'dev-analytics-key'
      },
      payment: {
        mchId: 'dev-mch-id'
      }
    }
  },
  
  [ENV_TYPES.TESTING]: {
    name: '测试环境',
    debug: true,
    
    cloud: {
      env: 'test-cloud-env-id',
      timeout: 15000,
      traceUser: true
    },
    
    api: {
      baseUrl: 'https://test-api.example.com',
      timeout: 10000,
      retryCount: 3,
      retryInterval: 1000
    },
    
    log: {
      level: 'INFO',
      enableConsole: true,
      enableUpload: true,
      uploadUrl: 'https://test-log.example.com/upload',
      maxLogCount: 500,
      uploadInterval: 30000
    },
    
    cache: {
      defaultExpireTime: 30 * 60 * 1000,
      maxCacheSize: 100,
      enablePersistent: true
    },
    
    user: {
      autoLogin: true,
      tokenExpireTime: 7 * 24 * 60 * 60 * 1000,
      enableGuestMode: true
    },
    
    features: {
      enableAnalytics: true,
      enableCrashReport: true,
      enablePerformanceMonitor: true,
      enableDebugPanel: true
    },
    
    thirdParty: {
      analytics: {
        appKey: 'test-analytics-key'
      },
      payment: {
        mchId: 'test-mch-id'
      }
    }
  },
  
  [ENV_TYPES.STAGING]: {
    name: '预发布环境',
    debug: false,
    
    cloud: {
      env: 'staging-cloud-env-id',
      timeout: 15000,
      traceUser: true
    },
    
    api: {
      baseUrl: 'https://staging-api.example.com',
      timeout: 10000,
      retryCount: 3,
      retryInterval: 1000
    },
    
    log: {
      level: 'WARN',
      enableConsole: false,
      enableUpload: true,
      uploadUrl: 'https://staging-log.example.com/upload',
      maxLogCount: 200,
      uploadInterval: 30000
    },
    
    cache: {
      defaultExpireTime: 60 * 60 * 1000, // 1小时
      maxCacheSize: 200,
      enablePersistent: true
    },
    
    user: {
      autoLogin: false,
      tokenExpireTime: 7 * 24 * 60 * 60 * 1000,
      enableGuestMode: false
    },
    
    features: {
      enableAnalytics: true,
      enableCrashReport: true,
      enablePerformanceMonitor: true,
      enableDebugPanel: false
    },
    
    thirdParty: {
      analytics: {
        appKey: 'staging-analytics-key'
      },
      payment: {
        mchId: 'staging-mch-id'
      }
    }
  },
  
  [ENV_TYPES.PRODUCTION]: {
    name: '生产环境',
    debug: false,
    
    cloud: {
      env: 'prod-cloud-env-id',
      timeout: 15000,
      traceUser: false
    },
    
    api: {
      baseUrl: 'https://api.example.com',
      timeout: 8000,
      retryCount: 2,
      retryInterval: 2000
    },
    
    log: {
      level: 'ERROR',
      enableConsole: false,
      enableUpload: true,
      uploadUrl: 'https://log.example.com/upload',
      maxLogCount: 100,
      uploadInterval: 60000
    },
    
    cache: {
      defaultExpireTime: 2 * 60 * 60 * 1000, // 2小时
      maxCacheSize: 300,
      enablePersistent: true
    },
    
    user: {
      autoLogin: false,
      tokenExpireTime: 30 * 24 * 60 * 60 * 1000, // 30天
      enableGuestMode: false
    },
    
    features: {
      enableAnalytics: true,
      enableCrashReport: true,
      enablePerformanceMonitor: false,
      enableDebugPanel: false
    },
    
    thirdParty: {
      analytics: {
        appKey: 'prod-analytics-key'
      },
      payment: {
        mchId: 'prod-mch-id'
      }
    }
  }
};

/**
 * 获取当前环境类型
 */
export function getCurrentEnv() {
  return CURRENT_ENV;
}

/**
 * 设置当前环境
 * @param {string} env - 环境类型
 */
export function setCurrentEnv(env) {
  if (!ENV_CONFIGS[env]) {
    throw new Error(`不支持的环境类型: ${env}`);
  }
  CURRENT_ENV = env;
  console.log(`[EnvConfig] 环境已切换到: ${ENV_CONFIGS[env].name}`);
}

/**
 * 获取当前环境配置
 */
export function getEnvConfig() {
  return ENV_CONFIGS[CURRENT_ENV];
}

/**
 * 获取指定环境配置
 * @param {string} env - 环境类型
 */
export function getEnvConfigByType(env) {
  return ENV_CONFIGS[env];
}

/**
 * 获取当前环境的特定配置
 * @param {string} key - 配置键名
 */
export function getConfig(key) {
  const config = getEnvConfig();
  return config[key];
}

/**
 * 检查是否为开发环境
 */
export function isDevelopment() {
  return CURRENT_ENV === ENV_TYPES.DEVELOPMENT;
}

/**
 * 检查是否为测试环境
 */
export function isTesting() {
  return CURRENT_ENV === ENV_TYPES.TESTING;
}

/**
 * 检查是否为预发布环境
 */
export function isStaging() {
  return CURRENT_ENV === ENV_TYPES.STAGING;
}

/**
 * 检查是否为生产环境
 */
export function isProduction() {
  return CURRENT_ENV === ENV_TYPES.PRODUCTION;
}

/**
 * 检查是否为调试模式
 */
export function isDebug() {
  return getEnvConfig().debug;
}

/**
 * 获取环境信息摘要
 */
export function getEnvSummary() {
  const config = getEnvConfig();
  return {
    type: CURRENT_ENV,
    name: config.name,
    debug: config.debug,
    cloudEnv: config.cloud.env,
    apiBaseUrl: config.api.baseUrl
  };
}

// 导出默认配置
export default {
  ENV_TYPES,
  getCurrentEnv,
  setCurrentEnv,
  getEnvConfig,
  getEnvConfigByType,
  getConfig,
  isDevelopment,
  isTesting,
  isStaging,
  isProduction,
  isDebug,
  getEnvSummary
};
