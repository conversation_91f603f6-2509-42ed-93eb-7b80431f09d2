// pages/feedback/feedback.js
const app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    activeTab: 'submit', // submit: 提交反馈, history: 历史反馈
    
    // 反馈表单数据
    feedbackForm: {
      type: 'bug', // bug: 问题反馈, suggestion: 建议, other: 其他
      title: '',
      content: '',
      contact: '',
      images: []
    },
    
    // 反馈类型选项
    feedbackTypes: [
      { value: 'bug', label: '问题反馈', icon: '🐛' },
      { value: 'suggestion', label: '功能建议', icon: '💡' },
      { value: 'other', label: '其他', icon: '💬' }
    ],
    
    // 历史反馈列表
    feedbackHistory: [
      {
        id: 1,
        type: 'bug',
        title: '登录页面加载缓慢',
        content: '在网络较差的情况下，登录页面加载时间过长，希望能优化一下。',
        status: 'processing', // pending: 待处理, processing: 处理中, resolved: 已解决
        createTime: '2024-01-15 10:30',
        reply: ''
      },
      {
        id: 2,
        type: 'suggestion',
        title: '希望增加夜间模式',
        content: '建议增加夜间模式功能，方便晚上使用。',
        status: 'resolved',
        createTime: '2024-01-10 14:20',
        reply: '感谢您的建议，夜间模式功能已在开发计划中，预计下个版本上线。'
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 获取用户信息作为默认联系方式
    const userManager = app.getManager('user');
    const userInfo = userManager.getUserInfo();
    if (userInfo && userInfo.nickName) {
      this.setData({
        'feedbackForm.contact': userInfo.nickName
      });
    }
  },

  /**
   * 切换标签页
   */
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({
      activeTab: tab
    });
  },

  /**
   * 反馈类型选择
   */
  onTypeChange(e) {
    const type = e.detail.value;
    this.setData({
      'feedbackForm.type': type
    });
  },

  /**
   * 标题输入
   */
  onTitleInput(e) {
    this.setData({
      'feedbackForm.title': e.detail.value
    });
  },

  /**
   * 内容输入
   */
  onContentInput(e) {
    this.setData({
      'feedbackForm.content': e.detail.value
    });
  },

  /**
   * 联系方式输入
   */
  onContactInput(e) {
    this.setData({
      'feedbackForm.contact': e.detail.value
    });
  },

  /**
   * 选择图片
   */
  onChooseImage() {
    const currentImages = this.data.feedbackForm.images;
    const maxCount = 3 - currentImages.length;

    if (maxCount <= 0) {
      wx.showToast({
        title: '最多上传3张图片',
        icon: 'none'
      });
      return;
    }

    wx.chooseImage({
      count: maxCount,
      sourceType: ['album', 'camera'],
      success: (res) => {
        const newImages = res.tempFilePaths;
        this.setData({
          'feedbackForm.images': [...currentImages, ...newImages]
        });
      }
    });
  },

  /**
   * 删除图片
   */
  onDeleteImage(e) {
    const index = e.currentTarget.dataset.index;
    const images = this.data.feedbackForm.images;
    images.splice(index, 1);
    this.setData({
      'feedbackForm.images': images
    });
  },

  /**
   * 预览图片
   */
  onPreviewImage(e) {
    const current = e.currentTarget.dataset.src;
    const urls = this.data.feedbackForm.images;
    
    wx.previewImage({
      current,
      urls
    });
  },

  /**
   * 提交反馈
   */
  onSubmitFeedback() {
    const form = this.data.feedbackForm;
    
    // 表单验证
    if (!form.title.trim()) {
      wx.showToast({
        title: '请输入反馈标题',
        icon: 'none'
      });
      return;
    }
    
    if (!form.content.trim()) {
      wx.showToast({
        title: '请输入反馈内容',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '提交中...'
    });

    // 模拟提交反馈
    setTimeout(() => {
      wx.hideLoading();
      wx.showToast({
        title: '提交成功',
        icon: 'success'
      });
      
      // 重置表单
      this.setData({
        feedbackForm: {
          type: 'bug',
          title: '',
          content: '',
          contact: form.contact, // 保留联系方式
          images: []
        }
      });
      
      // 切换到历史反馈页面
      this.setData({
        activeTab: 'history'
      });
    }, 1500);
  },

  /**
   * 查看反馈详情
   */
  onViewFeedback(e) {
    const id = e.currentTarget.dataset.id;
    wx.showToast({
      title: `查看反馈 ${id}`,
      icon: 'none'
    });
    // TODO: 跳转到反馈详情页
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉刷新
   */
  onPullDownRefresh() {
    // 刷新历史反馈
    setTimeout(() => {
      wx.stopPullDownRefresh();
      wx.showToast({
        title: '刷新成功',
        icon: 'success'
      });
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'UW产品反馈',
      path: '/pages/feedback/feedback'
    };
  }
});
