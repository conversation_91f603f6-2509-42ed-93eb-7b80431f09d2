/**
 * 缓存管理器
 * 统一管理本地缓存，支持过期策略和自动清理
 */

import { CACHE_CONFIG, EVENT_NAMES } from '../constants/config.js';

class CacheManager {
  constructor() {
    this.cache = new Map();
    this.timers = new Map();
    this.eventListeners = new Map();
    this.isInitialized = false;
  }

  /**
   * 初始化缓存管理器
   */
  init() {
    try {
      // 从本地存储恢复缓存
      this.loadFromStorage();
      
      // 清理过期缓存
      this.clearExpired();
      
      this.isInitialized = true;
      console.log('[CacheManager] 缓存管理器初始化成功');
      
      return true;
    } catch (error) {
      console.error('[CacheManager] 缓存管理器初始化失败:', error);
      return false;
    }
  }

  /**
   * 设置缓存
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} expireTime - 过期时间（毫秒），0表示永不过期
   * @param {boolean} persistent - 是否持久化到本地存储
   */
  set(key, value, expireTime = CACHE_CONFIG.DEFAULT_EXPIRE_TIME, persistent = false) {
    try {
      const fullKey = CACHE_CONFIG.KEY_PREFIX + key;
      const now = Date.now();
      
      const cacheItem = {
        value,
        createTime: now,
        expireTime: expireTime > 0 ? now + expireTime : 0,
        persistent,
        accessCount: 0,
        lastAccess: now
      };

      // 检查缓存大小限制
      if (this.cache.size >= CACHE_CONFIG.MAX_CACHE_SIZE) {
        this.clearLeastUsed();
      }

      this.cache.set(fullKey, cacheItem);

      // 设置过期定时器
      if (expireTime > 0) {
        this.setExpireTimer(fullKey, expireTime);
      }

      // 持久化到本地存储
      if (persistent) {
        this.saveToStorage(fullKey, cacheItem);
      }

      console.log(`[CacheManager] 缓存设置成功: ${key}`, { expireTime, persistent });
      return true;
    } catch (error) {
      console.error(`[CacheManager] 缓存设置失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 获取缓存
   * @param {string} key - 缓存键
   * @param {any} defaultValue - 默认值
   */
  get(key, defaultValue = null) {
    try {
      const fullKey = CACHE_CONFIG.KEY_PREFIX + key;
      const cacheItem = this.cache.get(fullKey);

      if (!cacheItem) {
        return defaultValue;
      }

      // 检查是否过期
      if (this.isExpired(cacheItem)) {
        this.delete(key);
        return defaultValue;
      }

      // 更新访问信息
      cacheItem.accessCount++;
      cacheItem.lastAccess = Date.now();

      console.log(`[CacheManager] 缓存获取成功: ${key}`);
      return cacheItem.value;
    } catch (error) {
      console.error(`[CacheManager] 缓存获取失败: ${key}`, error);
      return defaultValue;
    }
  }

  /**
   * 删除缓存
   * @param {string} key - 缓存键
   */
  delete(key) {
    try {
      const fullKey = CACHE_CONFIG.KEY_PREFIX + key;
      const cacheItem = this.cache.get(fullKey);

      if (cacheItem) {
        // 清除定时器
        this.clearExpireTimer(fullKey);
        
        // 从内存中删除
        this.cache.delete(fullKey);
        
        // 从本地存储中删除
        if (cacheItem.persistent) {
          this.removeFromStorage(fullKey);
        }

        console.log(`[CacheManager] 缓存删除成功: ${key}`);
        return true;
      }
      
      return false;
    } catch (error) {
      console.error(`[CacheManager] 缓存删除失败: ${key}`, error);
      return false;
    }
  }

  /**
   * 检查缓存是否存在且未过期
   * @param {string} key - 缓存键
   */
  has(key) {
    const fullKey = CACHE_CONFIG.KEY_PREFIX + key;
    const cacheItem = this.cache.get(fullKey);
    
    if (!cacheItem) {
      return false;
    }

    if (this.isExpired(cacheItem)) {
      this.delete(key);
      return false;
    }

    return true;
  }

  /**
   * 清空所有缓存
   * @param {boolean} includePersistent - 是否包括持久化缓存
   */
  clear(includePersistent = false) {
    try {
      const keysToDelete = [];
      
      for (const [fullKey, cacheItem] of this.cache.entries()) {
        if (includePersistent || !cacheItem.persistent) {
          keysToDelete.push(fullKey);
        }
      }

      keysToDelete.forEach(fullKey => {
        const key = fullKey.replace(CACHE_CONFIG.KEY_PREFIX, '');
        this.delete(key);
      });

      this.emit(EVENT_NAMES.CACHE_CLEAR, { includePersistent });
      console.log('[CacheManager] 缓存清空完成', { count: keysToDelete.length });
      
      return true;
    } catch (error) {
      console.error('[CacheManager] 缓存清空失败:', error);
      return false;
    }
  }

  /**
   * 清理过期缓存
   */
  clearExpired() {
    try {
      const expiredKeys = [];
      
      for (const [fullKey, cacheItem] of this.cache.entries()) {
        if (this.isExpired(cacheItem)) {
          expiredKeys.push(fullKey.replace(CACHE_CONFIG.KEY_PREFIX, ''));
        }
      }

      expiredKeys.forEach(key => this.delete(key));
      
      if (expiredKeys.length > 0) {
        console.log('[CacheManager] 过期缓存清理完成', { count: expiredKeys.length });
      }
      
      return expiredKeys.length;
    } catch (error) {
      console.error('[CacheManager] 过期缓存清理失败:', error);
      return 0;
    }
  }

  /**
   * 清理最少使用的缓存
   */
  clearLeastUsed() {
    try {
      const entries = Array.from(this.cache.entries());
      
      // 按访问次数和最后访问时间排序
      entries.sort((a, b) => {
        const [, itemA] = a;
        const [, itemB] = b;
        
        if (itemA.accessCount !== itemB.accessCount) {
          return itemA.accessCount - itemB.accessCount;
        }
        
        return itemA.lastAccess - itemB.lastAccess;
      });

      // 删除最少使用的缓存项
      const [fullKey] = entries[0];
      const key = fullKey.replace(CACHE_CONFIG.KEY_PREFIX, '');
      this.delete(key);
      
      console.log('[CacheManager] 最少使用缓存清理完成:', key);
    } catch (error) {
      console.error('[CacheManager] 最少使用缓存清理失败:', error);
    }
  }

  /**
   * 获取缓存统计信息
   */
  getStats() {
    const stats = {
      total: this.cache.size,
      persistent: 0,
      expired: 0,
      memoryUsage: 0
    };

    for (const [, cacheItem] of this.cache.entries()) {
      if (cacheItem.persistent) {
        stats.persistent++;
      }
      
      if (this.isExpired(cacheItem)) {
        stats.expired++;
      }
      
      // 简单估算内存使用量
      stats.memoryUsage += JSON.stringify(cacheItem).length;
    }

    return stats;
  }

  /**
   * 检查缓存项是否过期
   * @param {Object} cacheItem - 缓存项
   */
  isExpired(cacheItem) {
    if (cacheItem.expireTime === 0) {
      return false; // 永不过期
    }
    
    return Date.now() > cacheItem.expireTime;
  }

  /**
   * 设置过期定时器
   * @param {string} fullKey - 完整缓存键
   * @param {number} expireTime - 过期时间
   */
  setExpireTimer(fullKey, expireTime) {
    this.clearExpireTimer(fullKey);
    
    const timer = setTimeout(() => {
      const key = fullKey.replace(CACHE_CONFIG.KEY_PREFIX, '');
      this.delete(key);
    }, expireTime);
    
    this.timers.set(fullKey, timer);
  }

  /**
   * 清除过期定时器
   * @param {string} fullKey - 完整缓存键
   */
  clearExpireTimer(fullKey) {
    const timer = this.timers.get(fullKey);
    if (timer) {
      clearTimeout(timer);
      this.timers.delete(fullKey);
    }
  }

  /**
   * 保存到本地存储
   * @param {string} fullKey - 完整缓存键
   * @param {Object} cacheItem - 缓存项
   */
  saveToStorage(fullKey, cacheItem) {
    try {
      wx.setStorageSync(fullKey, cacheItem);
    } catch (error) {
      console.error('[CacheManager] 保存到本地存储失败:', error);
    }
  }

  /**
   * 从本地存储删除
   * @param {string} fullKey - 完整缓存键
   */
  removeFromStorage(fullKey) {
    try {
      wx.removeStorageSync(fullKey);
    } catch (error) {
      console.error('[CacheManager] 从本地存储删除失败:', error);
    }
  }

  /**
   * 从本地存储加载缓存
   */
  loadFromStorage() {
    try {
      const storageInfo = wx.getStorageInfoSync();
      
      storageInfo.keys.forEach(key => {
        if (key.startsWith(CACHE_CONFIG.KEY_PREFIX)) {
          try {
            const cacheItem = wx.getStorageSync(key);
            if (cacheItem && cacheItem.persistent) {
              // 检查是否过期
              if (!this.isExpired(cacheItem)) {
                this.cache.set(key, cacheItem);
                
                // 重新设置过期定时器
                if (cacheItem.expireTime > 0) {
                  const remainingTime = cacheItem.expireTime - Date.now();
                  if (remainingTime > 0) {
                    this.setExpireTimer(key, remainingTime);
                  }
                }
              } else {
                // 删除过期的持久化缓存
                wx.removeStorageSync(key);
              }
            }
          } catch (error) {
            console.error('[CacheManager] 加载缓存项失败:', key, error);
          }
        }
      });
      
      console.log('[CacheManager] 从本地存储加载缓存完成');
    } catch (error) {
      console.error('[CacheManager] 从本地存储加载缓存失败:', error);
    }
  }

  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * 移除事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('[CacheManager] 事件回调执行失败:', error);
        }
      });
    }
  }
}

// 创建单例
const cacheManager = new CacheManager();

export default cacheManager;
