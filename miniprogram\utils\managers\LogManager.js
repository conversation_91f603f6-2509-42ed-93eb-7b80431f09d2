/**
 * 日志管理器
 * 统一管理日志记录、分级日志、日志上报等功能
 */

import { LOG_LEVELS, EVENT_NAMES } from '../constants/config.js';

class LogManager {
  constructor() {
    this.currentLevel = LOG_LEVELS.INFO;
    this.logs = [];
    this.maxLogCount = 1000;
    this.isInitialized = false;
    this.uploadQueue = [];
    this.isUploading = false;
    this.eventListeners = new Map();
  }

  /**
   * 初始化日志管理器
   * @param {Object} options - 配置选项
   */
  init(options = {}) {
    try {
      const {
        level = LOG_LEVELS.INFO,
        maxLogCount = 1000,
        enableUpload = false,
        uploadUrl = '',
        uploadInterval = 60000 // 1分钟
      } = options;

      this.currentLevel = level;
      this.maxLogCount = maxLogCount;
      this.enableUpload = enableUpload;
      this.uploadUrl = uploadUrl;
      this.uploadInterval = uploadInterval;

      // 从本地存储恢复日志
      this.loadLogsFromStorage();

      // 设置定时上报
      if (this.enableUpload && this.uploadUrl) {
        this.startUploadTimer();
      }

      this.isInitialized = true;
      console.log('[LogManager] 日志管理器初始化成功', options);
      
      return true;
    } catch (error) {
      console.error('[LogManager] 日志管理器初始化失败:', error);
      return false;
    }
  }

  /**
   * 设置日志级别
   * @param {number} level - 日志级别
   */
  setLevel(level) {
    if (Object.values(LOG_LEVELS).includes(level)) {
      this.currentLevel = level;
      this.info('日志级别已设置为:', this.getLevelName(level));
    } else {
      this.error('无效的日志级别:', level);
    }
  }

  /**
   * 获取日志级别名称
   * @param {number} level - 日志级别
   */
  getLevelName(level) {
    const levelNames = {
      [LOG_LEVELS.DEBUG]: 'DEBUG',
      [LOG_LEVELS.INFO]: 'INFO',
      [LOG_LEVELS.WARN]: 'WARN',
      [LOG_LEVELS.ERROR]: 'ERROR'
    };
    return levelNames[level] || 'UNKNOWN';
  }

  /**
   * 记录日志
   * @param {number} level - 日志级别
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   * @param {string} category - 日志分类
   */
  log(level, message, data = null, category = 'default') {
    if (level < this.currentLevel) {
      return; // 低于当前级别的日志不记录
    }

    const logEntry = {
      id: this.generateLogId(),
      level,
      levelName: this.getLevelName(level),
      message,
      data,
      category,
      timestamp: Date.now(),
      time: new Date().toISOString(),
      userAgent: this.getUserAgent(),
      page: this.getCurrentPage()
    };

    // 添加到日志列表
    this.logs.push(logEntry);

    // 控制日志数量
    if (this.logs.length > this.maxLogCount) {
      this.logs.shift(); // 删除最旧的日志
    }

    // 输出到控制台
    this.outputToConsole(logEntry);

    // 保存到本地存储
    this.saveLogsToStorage();

    // 添加到上传队列
    if (this.enableUpload) {
      this.addToUploadQueue(logEntry);
    }

    // 触发日志事件
    this.emit('log', logEntry);

    // 如果是错误日志，触发错误事件
    if (level === LOG_LEVELS.ERROR) {
      this.emit(EVENT_NAMES.ERROR_OCCURRED, logEntry);
    }
  }

  /**
   * DEBUG 级别日志
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   * @param {string} category - 日志分类
   */
  debug(message, data = null, category = 'default') {
    this.log(LOG_LEVELS.DEBUG, message, data, category);
  }

  /**
   * INFO 级别日志
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   * @param {string} category - 日志分类
   */
  info(message, data = null, category = 'default') {
    this.log(LOG_LEVELS.INFO, message, data, category);
  }

  /**
   * WARN 级别日志
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   * @param {string} category - 日志分类
   */
  warn(message, data = null, category = 'default') {
    this.log(LOG_LEVELS.WARN, message, data, category);
  }

  /**
   * ERROR 级别日志
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   * @param {string} category - 日志分类
   */
  error(message, data = null, category = 'default') {
    this.log(LOG_LEVELS.ERROR, message, data, category);
  }

  /**
   * 获取日志列表
   * @param {Object} filter - 过滤条件
   */
  getLogs(filter = {}) {
    let filteredLogs = [...this.logs];

    // 按级别过滤
    if (filter.level !== undefined) {
      filteredLogs = filteredLogs.filter(log => log.level >= filter.level);
    }

    // 按分类过滤
    if (filter.category) {
      filteredLogs = filteredLogs.filter(log => log.category === filter.category);
    }

    // 按时间范围过滤
    if (filter.startTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= filter.startTime);
    }
    if (filter.endTime) {
      filteredLogs = filteredLogs.filter(log => log.timestamp <= filter.endTime);
    }

    // 按关键词过滤
    if (filter.keyword) {
      const keyword = filter.keyword.toLowerCase();
      filteredLogs = filteredLogs.filter(log => 
        log.message.toLowerCase().includes(keyword) ||
        (log.data && JSON.stringify(log.data).toLowerCase().includes(keyword))
      );
    }

    // 限制数量
    if (filter.limit) {
      filteredLogs = filteredLogs.slice(-filter.limit);
    }

    return filteredLogs;
  }

  /**
   * 清空日志
   * @param {Object} filter - 清空条件
   */
  clearLogs(filter = {}) {
    if (Object.keys(filter).length === 0) {
      // 清空所有日志
      this.logs = [];
    } else {
      // 按条件清空
      const logsToKeep = this.logs.filter(log => {
        if (filter.level !== undefined && log.level >= filter.level) {
          return false;
        }
        if (filter.category && log.category === filter.category) {
          return false;
        }
        if (filter.startTime && log.timestamp >= filter.startTime) {
          return false;
        }
        if (filter.endTime && log.timestamp <= filter.endTime) {
          return false;
        }
        return true;
      });
      this.logs = logsToKeep;
    }

    this.saveLogsToStorage();
    this.info('日志已清空', filter);
  }

  /**
   * 导出日志
   * @param {Object} filter - 导出条件
   */
  exportLogs(filter = {}) {
    const logs = this.getLogs(filter);
    const exportData = {
      exportTime: new Date().toISOString(),
      totalCount: logs.length,
      logs: logs
    };

    return JSON.stringify(exportData, null, 2);
  }

  /**
   * 手动上传日志
   */
  async uploadLogs() {
    if (!this.enableUpload || !this.uploadUrl) {
      this.warn('日志上传未启用或未配置上传地址');
      return false;
    }

    if (this.isUploading) {
      this.warn('日志正在上传中，请稍后再试');
      return false;
    }

    if (this.uploadQueue.length === 0) {
      this.info('没有待上传的日志');
      return true;
    }

    try {
      this.isUploading = true;
      
      const uploadData = {
        logs: [...this.uploadQueue],
        deviceInfo: this.getDeviceInfo(),
        timestamp: Date.now()
      };

      const result = await wx.request({
        url: this.uploadUrl,
        method: 'POST',
        data: uploadData,
        header: {
          'Content-Type': 'application/json'
        }
      });

      if (result.statusCode === 200) {
        this.uploadQueue = []; // 清空上传队列
        this.info('日志上传成功', { count: uploadData.logs.length });
        return true;
      } else {
        this.error('日志上传失败', { statusCode: result.statusCode, data: result.data });
        return false;
      }
    } catch (error) {
      this.error('日志上传异常', error);
      return false;
    } finally {
      this.isUploading = false;
    }
  }

  /**
   * 生成日志ID
   */
  generateLogId() {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 获取用户代理信息
   */
  getUserAgent() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      return `${systemInfo.platform} ${systemInfo.system} WeChat/${systemInfo.version}`;
    } catch (error) {
      return 'Unknown';
    }
  }

  /**
   * 获取当前页面
   */
  getCurrentPage() {
    try {
      const pages = getCurrentPages();
      if (pages.length > 0) {
        return pages[pages.length - 1].route;
      }
    } catch (error) {
      // ignore
    }
    return 'Unknown';
  }

  /**
   * 获取设备信息
   */
  getDeviceInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync();
      return {
        platform: systemInfo.platform,
        system: systemInfo.system,
        version: systemInfo.version,
        model: systemInfo.model,
        brand: systemInfo.brand,
        screenWidth: systemInfo.screenWidth,
        screenHeight: systemInfo.screenHeight
      };
    } catch (error) {
      return {};
    }
  }

  /**
   * 输出到控制台
   * @param {Object} logEntry - 日志条目
   */
  outputToConsole(logEntry) {
    const { level, message, data, time, category } = logEntry;
    const prefix = `[${time}] [${logEntry.levelName}] [${category}]`;
    
    switch (level) {
      case LOG_LEVELS.DEBUG:
        console.debug(prefix, message, data);
        break;
      case LOG_LEVELS.INFO:
        console.info(prefix, message, data);
        break;
      case LOG_LEVELS.WARN:
        console.warn(prefix, message, data);
        break;
      case LOG_LEVELS.ERROR:
        console.error(prefix, message, data);
        break;
      default:
        console.log(prefix, message, data);
    }
  }

  /**
   * 保存日志到本地存储
   */
  saveLogsToStorage() {
    try {
      // 只保存最近的日志到本地存储
      const recentLogs = this.logs.slice(-100);
      wx.setStorageSync('app_logs', recentLogs);
    } catch (error) {
      console.error('[LogManager] 保存日志到本地存储失败:', error);
    }
  }

  /**
   * 从本地存储加载日志
   */
  loadLogsFromStorage() {
    try {
      const savedLogs = wx.getStorageSync('app_logs');
      if (Array.isArray(savedLogs)) {
        this.logs = savedLogs;
      }
    } catch (error) {
      console.error('[LogManager] 从本地存储加载日志失败:', error);
    }
  }

  /**
   * 添加到上传队列
   * @param {Object} logEntry - 日志条目
   */
  addToUploadQueue(logEntry) {
    this.uploadQueue.push(logEntry);
    
    // 控制上传队列大小
    if (this.uploadQueue.length > 500) {
      this.uploadQueue.shift();
    }
  }

  /**
   * 启动上传定时器
   */
  startUploadTimer() {
    setInterval(() => {
      if (this.uploadQueue.length > 0) {
        this.uploadLogs();
      }
    }, this.uploadInterval);
  }

  /**
   * 事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  on(event, callback) {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event).push(callback);
  }

  /**
   * 移除事件监听
   * @param {string} event - 事件名称
   * @param {Function} callback - 回调函数
   */
  off(event, callback) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  /**
   * 触发事件
   * @param {string} event - 事件名称
   * @param {any} data - 事件数据
   */
  emit(event, data) {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('[LogManager] 事件回调执行失败:', error);
        }
      });
    }
  }
}

// 创建单例
const logManager = new LogManager();

export default logManager;
