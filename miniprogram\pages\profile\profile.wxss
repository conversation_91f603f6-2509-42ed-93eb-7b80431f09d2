/* pages/profile/profile.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 用户信息区域 */
.user-section {
  background: linear-gradient(135deg, #3cc51f 0%, #2aa515 100%);
  padding: 60rpx 40rpx 40rpx;
  color: #fff;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar-wrapper {
  margin-right: 32rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
}

.user-details {
  flex: 1;
}

.username {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.user-desc {
  font-size: 26rpx;
  opacity: 0.8;
}

.user-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.action-btn, .logout-btn {
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  font-size: 24rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  background-color: transparent;
  color: #fff;
}

.action-btn:active, .logout-btn:active {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 登录提示 */
.login-prompt {
  display: flex;
  align-items: center;
}

.login-avatar {
  margin-right: 32rpx;
}

.login-text {
  flex: 1;
}

.login-title {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.login-desc {
  font-size: 26rpx;
  opacity: 0.8;
}

.login-btn {
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  background-color: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
}

.login-btn:active {
  background-color: rgba(255, 255, 255, 0.3);
}

/* 菜单区域 */
.menu-section {
  margin-top: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
  overflow: hidden;
}

.section-title {
  padding: 32rpx 32rpx 20rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.menu-list {
  
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #f5f5f5;
  transition: background-color 0.3s ease;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:active {
  background-color: #f8f8f8;
}

.menu-icon {
  width: 60rpx;
  font-size: 40rpx;
  text-align: center;
  margin-right: 24rpx;
}

.menu-title {
  flex: 1;
  font-size: 30rpx;
  color: #333;
}

.menu-extra {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.badge {
  background-color: #ff4757;
  color: #fff;
  font-size: 20rpx;
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  min-width: 32rpx;
  text-align: center;
}

.arrow {
  font-size: 28rpx;
  color: #999;
}

/* 环境信息区域 */
.env-section {
  margin: 20rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  border: 2rpx solid #ffd93d;
}

.env-info {
  padding: 32rpx;
}

.env-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.env-item:last-of-type {
  margin-bottom: 20rpx;
}

.env-label {
  font-size: 28rpx;
  color: #666;
}

.env-value {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.debug-on {
  color: #ff4757;
}

.debug-off {
  color: #2ed573;
}

.env-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  padding-top: 16rpx;
  border-top: 1rpx solid #f5f5f5;
}

/* 页脚 */
.footer {
  text-align: center;
  padding: 60rpx 40rpx 40rpx;
  color: #999;
}

.copyright {
  font-size: 24rpx;
  margin-bottom: 8rpx;
}

.version {
  font-size: 22rpx;
}
