<!--pages/products/products.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input-wrapper">
      <input 
        class="search-input" 
        placeholder="搜索产品..." 
        value="{{searchValue}}"
        bindinput="onSearchInput"
      />
      <view class="search-icon">🔍</view>
    </view>
  </view>

  <!-- 分类导航 -->
  <view class="category-nav">
    <scroll-view class="category-scroll" scroll-x="true">
      <view class="category-list">
        <view 
          wx:for="{{categories}}" 
          wx:key="id"
          class="category-item {{activeCategory === item.id ? 'active' : ''}}"
          data-category="{{item.id}}"
          bindtap="onCategoryTap"
        >
          {{item.name}}
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 产品列表 -->
  <view class="products-section">
    <view wx:if="{{filteredProducts.length === 0}}" class="empty-state">
      <view class="empty-icon">📦</view>
      <view class="empty-text">暂无相关产品</view>
    </view>
    
    <view wx:else class="products-grid">
      <view 
        wx:for="{{filteredProducts}}" 
        wx:key="id"
        class="product-card"
        data-id="{{item.id}}"
        bindtap="onProductTap"
      >
        <view class="product-image-wrapper">
          <image class="product-image" src="{{item.image}}" mode="aspectFill" />
        </view>
        <view class="product-info">
          <view class="product-name">{{item.name}}</view>
          <view class="product-description">{{item.description}}</view>
          <view class="product-price">¥{{item.price}}</view>
        </view>
      </view>
    </view>
  </view>
</view>
