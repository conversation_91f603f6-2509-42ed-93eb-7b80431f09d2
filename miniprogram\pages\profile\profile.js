// pages/profile/profile.js
const app = getApp();

Page({

  /**
   * 页面的初始数据
   */
  data: {
    userInfo: null,
    isLoggedIn: false,
    
    // 菜单项
    menuItems: [
      {
        id: 'orders',
        title: '我的订单',
        icon: '📦',
        arrow: true,
        badge: 0
      },
      {
        id: 'favorites',
        title: '我的收藏',
        icon: '❤️',
        arrow: true,
        badge: 0
      },
      {
        id: 'feedback',
        title: '我的反馈',
        icon: '💬',
        arrow: true,
        badge: 0
      }
    ],
    
    // 设置项
    settingItems: [
      {
        id: 'notifications',
        title: '消息通知',
        icon: '🔔',
        arrow: true,
        type: 'switch',
        value: true
      },
      {
        id: 'privacy',
        title: '隐私设置',
        icon: '🔒',
        arrow: true
      },
      {
        id: 'about',
        title: '关于我们',
        icon: 'ℹ️',
        arrow: true
      },
      {
        id: 'help',
        title: '帮助中心',
        icon: '❓',
        arrow: true
      }
    ],
    
    // 环境信息（开发模式下显示）
    envInfo: null,
    showEnvInfo: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.loadUserInfo();
    this.loadEnvInfo();
  },

  /**
   * 加载用户信息
   */
  loadUserInfo() {
    const userManager = app.getManager('user');
    const userInfo = userManager.getUserInfo();
    const isLoggedIn = userManager.isLoggedIn();
    
    this.setData({
      userInfo,
      isLoggedIn
    });
  },

  /**
   * 加载环境信息
   */
  loadEnvInfo() {
    if (app.isDevelopment()) {
      const envInfo = app.getEnvSummary();
      this.setData({
        envInfo,
        showEnvInfo: true
      });
    }
  },

  /**
   * 登录
   */
  onLogin() {
    const userManager = app.getManager('user');
    
    wx.showLoading({
      title: '登录中...'
    });
    
    userManager.login()
      .then((result) => {
        wx.hideLoading();
        if (result.success) {
          wx.showToast({
            title: '登录成功',
            icon: 'success'
          });
          this.loadUserInfo();
        } else {
          wx.showToast({
            title: result.message || '登录失败',
            icon: 'none'
          });
        }
      })
      .catch((error) => {
        wx.hideLoading();
        wx.showToast({
          title: '登录失败',
          icon: 'none'
        });
        console.error('登录失败:', error);
      });
  },

  /**
   * 获取用户信息
   */
  onGetUserProfile() {
    wx.getUserProfile({
      desc: '用于完善用户资料',
      success: (res) => {
        const userManager = app.getManager('user');
        userManager.updateUserInfo(res.userInfo);
        this.loadUserInfo();
        
        wx.showToast({
          title: '信息更新成功',
          icon: 'success'
        });
      },
      fail: (error) => {
        console.error('获取用户信息失败:', error);
      }
    });
  },

  /**
   * 退出登录
   */
  onLogout() {
    wx.showModal({
      title: '确认退出',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          const userManager = app.getManager('user');
          userManager.logout();
          this.loadUserInfo();
          
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  /**
   * 菜单项点击
   */
  onMenuItemTap(e) {
    const id = e.currentTarget.dataset.id;
    
    switch (id) {
      case 'orders':
        wx.showToast({
          title: '我的订单',
          icon: 'none'
        });
        break;
      case 'favorites':
        wx.showToast({
          title: '我的收藏',
          icon: 'none'
        });
        break;
      case 'feedback':
        wx.navigateTo({
          url: '/pages/feedback/feedback'
        });
        break;
      default:
        wx.showToast({
          title: `点击了 ${id}`,
          icon: 'none'
        });
    }
  },

  /**
   * 设置项点击
   */
  onSettingItemTap(e) {
    const id = e.currentTarget.dataset.id;
    
    switch (id) {
      case 'notifications':
        // 通知设置由开关控制，这里不处理
        break;
      case 'privacy':
        wx.showToast({
          title: '隐私设置',
          icon: 'none'
        });
        break;
      case 'about':
        this.showAboutModal();
        break;
      case 'help':
        wx.showToast({
          title: '帮助中心',
          icon: 'none'
        });
        break;
      default:
        wx.showToast({
          title: `点击了 ${id}`,
          icon: 'none'
        });
    }
  },

  /**
   * 通知开关切换
   */
  onNotificationSwitch(e) {
    const value = e.detail.value;
    const settingItems = this.data.settingItems;
    const index = settingItems.findIndex(item => item.id === 'notifications');
    
    if (index !== -1) {
      settingItems[index].value = value;
      this.setData({
        settingItems
      });
    }
    
    wx.showToast({
      title: value ? '已开启通知' : '已关闭通知',
      icon: 'success'
    });
  },

  /**
   * 显示关于弹窗
   */
  showAboutModal() {
    const envInfo = this.data.envInfo;
    const content = `UW小程序 v1.0.0\n\n一个专业的品牌展示平台\n\n${envInfo ? `当前环境: ${envInfo.name}` : ''}`;
    
    wx.showModal({
      title: '关于我们',
      content,
      showCancel: false,
      confirmText: '知道了'
    });
  },

  /**
   * 环境信息点击
   */
  onEnvInfoTap() {
    if (this.data.envInfo) {
      const envConfig = app.getEnvConfig();
      console.log('完整环境配置:', envConfig);
      
      wx.showModal({
        title: '环境信息',
        content: `环境: ${this.data.envInfo.name}\n类型: ${this.data.envInfo.type}\n调试: ${this.data.envInfo.debug ? '开启' : '关闭'}\n云环境: ${this.data.envInfo.cloudEnv}`,
        showCancel: false
      });
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.loadUserInfo();
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉刷新
   */
  onPullDownRefresh() {
    this.loadUserInfo();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: 'UW小程序',
      path: '/pages/index/index'
    };
  }
});
