/* pages/index/index.wxss */

/* 页面基础样式 */
page {
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* 轮播图样式 */
.banner-swiper {
  width: 100%;
  height: calc(100vh - 88rpx - 98rpx); /* 扣除导航栏(88rpx) + Tab栏(98rpx) */
}

/* 轮播图指示器样式 */
.banner-swiper .wx-swiper-dots.wx-swiper-dots-horizontal {
  margin-bottom: 40rpx;
}

.banner-swiper .wx-swiper-dot {
  width: 60rpx !important;
  height: 6rpx !important;
  border-radius: 3rpx !important;
  background-color: rgba(255, 255, 255, 0.5) !important;
  margin: 0 8rpx !important;
}

.banner-swiper .wx-swiper-dot.wx-swiper-dot-active {
  background-color: #fff !important;
}

.banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1) 0%,
    rgba(0, 0, 0, 0.3) 50%,
    rgba(0, 0, 0, 0.6) 100%
  );
  display: flex;
  align-items: center;
  justify-content: center;
}

.banner-content {
  text-align: center;
  color: #fff;
  padding: 0 40rpx;
}

.banner-title {
  font-size: 48rpx;
  font-weight: 700;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.banner-subtitle {
  font-size: 28rpx;
  opacity: 0.9;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}