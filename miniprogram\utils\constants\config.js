/**
 * 全局配置常量
 */

import EnvConfig from '../config/env.js';

// 日志级别
export const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3
};

// 日志级别名称映射
export const LOG_LEVEL_NAMES = {
  [LOG_LEVELS.DEBUG]: 'DEBUG',
  [LOG_LEVELS.INFO]: 'INFO',
  [LOG_LEVELS.WARN]: 'WARN',
  [LOG_LEVELS.ERROR]: 'ERROR'
};

// 获取当前环境配置
const currentEnvConfig = EnvConfig.getEnvConfig();

// 缓存配置（基于环境配置）
export const CACHE_CONFIG = {
  // 默认过期时间（毫秒）
  DEFAULT_EXPIRE_TIME: currentEnvConfig.cache.defaultExpireTime,
  // 最大缓存大小（条目数）
  MAX_CACHE_SIZE: currentEnvConfig.cache.maxCacheSize,
  // 缓存键前缀
  KEY_PREFIX: 'wx_cache_',
  // 用户相关缓存键
  USER_CACHE_KEY: 'user_info',
  // 系统配置缓存键
  SYSTEM_CONFIG_KEY: 'system_config',
  // 是否启用持久化
  ENABLE_PERSISTENT: currentEnvConfig.cache.enablePersistent
};

// 网络配置（基于环境配置）
export const NETWORK_CONFIG = {
  // 请求超时时间
  REQUEST_TIMEOUT: currentEnvConfig.api.timeout,
  // 重试次数
  MAX_RETRY_COUNT: currentEnvConfig.api.retryCount,
  // 重试间隔（毫秒）
  RETRY_INTERVAL: currentEnvConfig.api.retryInterval,
  // API 基础地址
  BASE_URL: currentEnvConfig.api.baseUrl
};

// 用户状态
export const USER_STATUS = {
  NOT_LOGIN: 'not_login',
  LOGGING: 'logging',
  LOGGED: 'logged',
  LOGIN_FAILED: 'login_failed'
};

// 网络状态
export const NETWORK_STATUS = {
  ONLINE: 'online',
  OFFLINE: 'offline',
  UNKNOWN: 'unknown'
};

// 云服务配置（基于环境配置）
export const CLOUD_CONFIG = {
  // 云环境ID
  ENV_ID: currentEnvConfig.cloud.env,
  // 云函数超时时间
  FUNCTION_TIMEOUT: currentEnvConfig.cloud.timeout,
  // 数据库查询限制
  DB_QUERY_LIMIT: 20,
  // 文件上传大小限制（字节）
  UPLOAD_SIZE_LIMIT: 10 * 1024 * 1024, // 10MB
  // 是否追踪用户
  TRACE_USER: currentEnvConfig.cloud.traceUser
};

// 事件名称
export const EVENT_NAMES = {
  USER_LOGIN: 'user_login',
  USER_LOGOUT: 'user_logout',
  NETWORK_CHANGE: 'network_change',
  CACHE_CLEAR: 'cache_clear',
  ERROR_OCCURRED: 'error_occurred'
};

// 存储键名
export const STORAGE_KEYS = {
  USER_TOKEN: 'user_token',
  USER_INFO: 'user_info',
  APP_CONFIG: 'app_config',
  LAST_LOGIN_TIME: 'last_login_time',
  ENV_CONFIG: 'env_config'
};

// 导出环境配置工具
export { EnvConfig };

// 环境相关的便捷方法
export const ENV_UTILS = {
  getCurrentEnv: EnvConfig.getCurrentEnv,
  getEnvConfig: EnvConfig.getEnvConfig,
  isDevelopment: EnvConfig.isDevelopment,
  isTesting: EnvConfig.isTesting,
  isStaging: EnvConfig.isStaging,
  isProduction: EnvConfig.isProduction,
  isDebug: EnvConfig.isDebug,
  getEnvSummary: EnvConfig.getEnvSummary
};
