// pages/index/index.js
Page({
  data: {
    // 轮播图数据
    banners: [
      {
        id: 1,
        image: '/images/banner/banner1.jpg',
        title: 'UW品牌新篇章',
        subtitle: '创新科技，品质生活',
        link: '/pages/products/products',
        type: 'page'
      },
      {
        id: 2,
        image: '/images/banner/banner2.jpg',
        title: '智能产品系列',
        subtitle: '科技改变生活方式',
        link: '/pages/products/products?category=electronics',
        type: 'page'
      },
      {
        id: 3,
        image: '/images/banner/banner3.jpg',
        title: '品质保证',
        subtitle: '每一个细节都精益求精',
        link: '/pages/feedback/feedback',
        type: 'page'
      }
    ],

    // 当前轮播图索引
    currentBannerIndex: 0
  },

  onLoad: function () {
    console.log('[Index] 首页加载');
  },

  /**
   * 轮播图切换
   */
  onBannerChange: function (e) {
    const current = e.detail.current;
    this.setData({
      currentBannerIndex: current
    });
  },

  /**
   * 轮播图点击
   */
  onBannerTap: function (e) {
    const index = e.currentTarget.dataset.index;
    const banner = this.data.banners[index];

    if (banner && banner.link) {
      if (banner.type === 'page') {
        wx.navigateTo({
          url: banner.link,
          fail: () => {
            wx.switchTab({
              url: banner.link
            });
          }
        });
      }
    }
  },

});