/**
 * 云开发环境配置文件
 * 兼容微信小程序云开发快速启动模板
 * 与 utils/config/env.js 集成，自动从环境配置中获取云环境列表
 */

// 导入环境配置
let EnvConfig;
try {
  // 尝试导入环境配置模块
  const envModule = require('./utils/config/env.js');
  EnvConfig = envModule.default || envModule;
} catch (error) {
  console.warn('[envList] 无法导入环境配置模块:', error);
  // 提供默认的空实现
  EnvConfig = {
    getEnvConfigByType: () => null,
    getCurrentEnv: () => 'development',
    getEnvConfig: () => ({ name: '默认环境', cloud: { env: '' } })
  };
}

// 从环境配置中提取云环境列表
function getEnvListFromConfig() {
  try {
    const envTypes = ['development', 'testing', 'staging', 'production'];
    const envList = [];

    envTypes.forEach(envType => {
      try {
        const config = EnvConfig.getEnvConfigByType(envType);
        if (config && config.cloud && config.cloud.env) {
          envList.push({
            alias: config.name,
            envId: config.cloud.env,
            type: envType
          });
        }
      } catch (error) {
        console.warn(`[envList] 获取 ${envType} 环境配置失败:`, error);
      }
    });

    return envList;
  } catch (error) {
    console.warn('[envList] 获取环境配置失败，使用空列表:', error);
    return [];
  }
}

// 动态生成环境列表
const envList = getEnvListFromConfig();

// 检测操作系统（保持原有逻辑）
const isMac = process.platform === 'darwin';

// 兼容原有接口
module.exports = {
  envList,
  isMac,
  // 新增方法
  getCurrentEnv: () => {
    try {
      return EnvConfig.getCurrentEnv();
    } catch (error) {
      return 'development';
    }
  },
  getCurrentEnvConfig: () => {
    try {
      const currentEnv = EnvConfig.getCurrentEnv();
      const config = EnvConfig.getEnvConfig();
      return {
        alias: config.name,
        envId: config.cloud.env,
        type: currentEnv
      };
    } catch (error) {
      return null;
    }
  }
};
